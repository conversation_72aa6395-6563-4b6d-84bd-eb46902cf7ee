import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import BulkOperations from '../BulkOperations';
import { useProducts } from '../../../hooks/useProducts';

// Mock the useProducts hook
jest.mock('../../../hooks/useProducts');
const mockUseProducts = useProducts as jest.MockedFunction<typeof useProducts>;

// Mock the excel import utilities
jest.mock('../../../utils/excelImport', () => ({
  parseImportFile: jest.fn(),
  generateImportTemplate: jest.fn()
}));

// Mock window.confirm
const mockConfirm = jest.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true
});

// Mock window.alert
const mockAlert = jest.fn();
Object.defineProperty(window, 'alert', {
  value: mockAlert,
  writable: true
});

describe('BulkOperations Component', () => {
  const mockProps = {
    selectedProducts: [],
    onClose: jest.fn(),
    onClearSelection: jest.fn()
  };

  const mockProductsHook = {
    products: [],
    loading: false,
    error: null,
    createProduct: jest.fn(),
    updateProduct: jest.fn(),
    deleteProduct: jest.fn(),
    updateStock: jest.fn(),
    getProductById: jest.fn(),
    getProductsByCategory: jest.fn(),
    getActiveProducts: jest.fn(),
    getInStockProducts: jest.fn(),
    getLowStockProducts: jest.fn(),
    getExpiringProducts: jest.fn(),
    getProductCategories: jest.fn(),
    searchProducts: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseProducts.mockReturnValue(mockProductsHook);
  });

  it('renders bulk operations modal with import option', () => {
    render(<BulkOperations {...mockProps} />);

    expect(screen.getByText(/Bulk Operations/)).toBeInTheDocument();
    expect(screen.getByText('Import')).toBeInTheDocument();
  });

  it('shows import interface when import is selected', () => {
    render(<BulkOperations {...mockProps} />);

    // Click on import option
    fireEvent.click(screen.getByText('Import'));

    expect(screen.getByText('Select File')).toBeInTheDocument();
    expect(screen.getAllByText('Download Template')[0]).toBeInTheDocument();
  });

  it('displays execution button when import preview is ready', async () => {
    const { parseImportFile } = require('../../../utils/excelImport');
    parseImportFile.mockResolvedValue({
      success: true,
      data: [
        { name: 'Test Product', category: 'Test', price: 100, stockQuantity: 10 }
      ]
    });

    render(<BulkOperations {...mockProps} />);

    // Click on import option
    fireEvent.click(screen.getByText('Import'));

    // Create a mock file
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const fileInput = screen.getByLabelText('Select File');
    
    // Simulate file selection
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    // Wait for the file to be processed
    await waitFor(() => {
      expect(screen.getByText(/Execute Import \(1 products\)/)).toBeInTheDocument();
    });
  });

  it('shows confirmation dialog when execution button is clicked', async () => {
    const { parseImportFile } = require('../../../utils/excelImport');
    parseImportFile.mockResolvedValue({
      success: true,
      data: [
        { name: 'Test Product', category: 'Test', price: 100, stockQuantity: 10 }
      ]
    });

    mockConfirm.mockReturnValue(true);
    mockProductsHook.createProduct.mockResolvedValue(undefined);

    render(<BulkOperations {...mockProps} />);

    // Click on import option
    fireEvent.click(screen.getByText('Import'));

    // Create a mock file
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const fileInput = screen.getByLabelText('Select File');
    
    // Simulate file selection
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    // Wait for the execution button to appear
    await waitFor(() => {
      expect(screen.getByText(/Execute Import \(1 products\)/)).toBeInTheDocument();
    });
    
    // Click the execution button
    fireEvent.click(screen.getByText(/Execute Import \(1 products\)/));
    
    // Verify confirmation dialog was shown
    expect(mockConfirm).toHaveBeenCalledWith(
      expect.stringContaining('Are you sure you want to import 1 product')
    );
  });

  it('executes import when confirmed', async () => {
    const { parseImportFile } = require('../../../utils/excelImport');
    parseImportFile.mockResolvedValue({
      success: true,
      data: [
        { name: 'Test Product', category: 'Test', price: 100, stockQuantity: 10 }
      ]
    });

    mockConfirm.mockReturnValue(true);
    mockProductsHook.createProduct.mockResolvedValue(undefined);

    render(<BulkOperations {...mockProps} />);

    // Click on import option
    fireEvent.click(screen.getByText('Import'));

    // Create a mock file
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const fileInput = screen.getByLabelText('Select File');
    
    // Simulate file selection
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    // Wait for the execution button to appear
    await waitFor(() => {
      expect(screen.getByText(/Execute Import \(1 products\)/)).toBeInTheDocument();
    });
    
    // Click the execution button
    fireEvent.click(screen.getByText(/Execute Import \(1 products\)/));
    
    // Wait for the import to complete
    await waitFor(() => {
      expect(mockProductsHook.createProduct).toHaveBeenCalledWith({
        name: 'Test Product',
        category: 'Test',
        price: 100,
        stockQuantity: 10
      });
    });
    
    // Verify success message
    expect(mockAlert).toHaveBeenCalledWith(
      expect.stringContaining('Successfully imported 1 product')
    );
  });

  it('cancels import when user declines confirmation', async () => {
    const { parseImportFile } = require('../../../utils/excelImport');
    parseImportFile.mockResolvedValue({
      success: true,
      data: [
        { name: 'Test Product', category: 'Test', price: 100, stockQuantity: 10 }
      ]
    });

    mockConfirm.mockReturnValue(false);

    render(<BulkOperations {...mockProps} />);

    // Click on import option
    fireEvent.click(screen.getByText('Import'));

    // Create a mock file
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const fileInput = screen.getByLabelText('Select File');
    
    // Simulate file selection
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    // Wait for the execution button to appear
    await waitFor(() => {
      expect(screen.getByText(/Execute Import \(1 products\)/)).toBeInTheDocument();
    });
    
    // Click the execution button
    fireEvent.click(screen.getByText(/Execute Import \(1 products\)/));
    
    // Verify that createProduct was not called
    expect(mockProductsHook.createProduct).not.toHaveBeenCalled();
  });
});
