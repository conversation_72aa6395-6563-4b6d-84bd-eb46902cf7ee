{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\inventory\\\\BulkOperations.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Upload, Download, Edit, Trash2, Package, AlertTriangle, CheckCircle, X, FileSpreadsheet } from 'lucide-react';\nimport { useProducts } from '../../hooks/useProducts';\nimport { parseImportFile, generateImportTemplate } from '../../utils/excelImport';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BulkOperations = ({\n  selectedProducts,\n  onClose,\n  onClearSelection\n}) => {\n  _s();\n  var _importResult$data, _importResult$data2, _importResult$data3, _importResult$data4, _importResult$data5;\n  const {\n    updateProduct,\n    deleteProduct,\n    createProduct\n  } = useProducts();\n  const [operation, setOperation] = useState(null);\n  const [bulkUpdateData, setBulkUpdateData] = useState({\n    category: '',\n    priceAdjustment: '',\n    adjustmentType: 'percentage',\n    stockAdjustment: '',\n    reorderLevel: '',\n    isActive: ''\n  });\n  const [processing, setProcessing] = useState(false);\n  const [importFile, setImportFile] = useState(null);\n  const [importResult, setImportResult] = useState(null);\n  const [showImportPreview, setShowImportPreview] = useState(false);\n\n  // Handle bulk price update\n  const handleBulkUpdate = async () => {\n    if (selectedProducts.length === 0) return;\n    setProcessing(true);\n    try {\n      for (const product of selectedProducts) {\n        const updates = {};\n\n        // Category update\n        if (bulkUpdateData.category) {\n          updates.category = bulkUpdateData.category;\n        }\n\n        // Price adjustment\n        if (bulkUpdateData.priceAdjustment) {\n          const adjustment = parseFloat(bulkUpdateData.priceAdjustment);\n          if (bulkUpdateData.adjustmentType === 'percentage') {\n            updates.price = product.price * (1 + adjustment / 100);\n          } else {\n            updates.price = product.price + adjustment;\n          }\n        }\n\n        // Stock adjustment\n        if (bulkUpdateData.stockAdjustment) {\n          const adjustment = parseInt(bulkUpdateData.stockAdjustment);\n          updates.stockQuantity = Math.max(0, product.stockQuantity + adjustment);\n        }\n\n        // Reorder level\n        if (bulkUpdateData.reorderLevel) {\n          updates.reorderLevel = parseInt(bulkUpdateData.reorderLevel);\n        }\n\n        // Active status\n        if (bulkUpdateData.isActive !== '') {\n          updates.isActive = bulkUpdateData.isActive === 'true';\n        }\n        if (Object.keys(updates).length > 0) {\n          await updateProduct(product.id, updates);\n        }\n      }\n      alert(`Successfully updated ${selectedProducts.length} products`);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Bulk update error:', error);\n      alert('Error updating products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle bulk delete\n  const handleBulkDelete = async () => {\n    if (selectedProducts.length === 0) return;\n    const confirmed = window.confirm(`Are you sure you want to delete ${selectedProducts.length} products? This action cannot be undone.`);\n    if (!confirmed) return;\n    setProcessing(true);\n    try {\n      for (const product of selectedProducts) {\n        await deleteProduct(product.id);\n      }\n      alert(`Successfully deleted ${selectedProducts.length} products`);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Bulk delete error:', error);\n      alert('Error deleting products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Export products to CSV\n  const handleExport = () => {\n    const csvHeaders = ['ID', 'Name', 'Description', 'Category', 'Price', 'Stock Quantity', 'Reorder Level', 'Has Expiry', 'Expiry Date', 'Is Active', 'Created At'];\n    const csvData = selectedProducts.map(product => {\n      var _product$expiryDate;\n      return [product.id, product.name, product.description, product.category, product.price, product.stockQuantity, product.reorderLevel, product.hasExpiry, ((_product$expiryDate = product.expiryDate) === null || _product$expiryDate === void 0 ? void 0 : _product$expiryDate.toISOString()) || '', product.isActive, product.createdAt.toISOString()];\n    });\n    const csvContent = [csvHeaders, ...csvData].map(row => row.map(field => `\"${field}\"`).join(',')).join('\\n');\n    const blob = new Blob([csvContent], {\n      type: 'text/csv'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `products-export-${new Date().toISOString().split('T')[0]}.csv`;\n    link.click();\n    window.URL.revokeObjectURL(url);\n    alert(`Exported ${selectedProducts.length} products to CSV`);\n  };\n\n  // Handle file parsing and preview\n  const handleFileSelect = async file => {\n    setImportFile(file);\n    setImportResult(null);\n    setShowImportPreview(false);\n    if (!file) return;\n    setProcessing(true);\n    try {\n      const result = await parseImportFile(file);\n      setImportResult(result);\n      if (result.success && result.data && result.data.length > 0) {\n        setShowImportPreview(true);\n      } else if (result.errors) {\n        alert(`Import validation failed:\\n${result.errors.join('\\n')}`);\n      }\n    } catch (error) {\n      console.error('File parsing error:', error);\n      alert('Error parsing file. Please check the file format.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle actual import after preview confirmation\n  const handleConfirmImport = async () => {\n    if (!importResult || !importResult.data) return;\n\n    // Show confirmation dialog\n    const totalProducts = importResult.data.length;\n    const confirmMessage = `Are you sure you want to import ${totalProducts} product${totalProducts > 1 ? 's' : ''} into your inventory?\\n\\nThis action cannot be undone.`;\n    if (!window.confirm(confirmMessage)) {\n      return;\n    }\n    setProcessing(true);\n    try {\n      let importedCount = 0;\n      let failedCount = 0;\n      const failedProducts = [];\n      for (const productData of importResult.data) {\n        try {\n          await createProduct(productData);\n          importedCount++;\n        } catch (error) {\n          console.error('Error creating product:', error);\n          failedCount++;\n          failedProducts.push(productData.name || 'Unknown product');\n        }\n      }\n\n      // Show detailed results\n      if (importedCount > 0) {\n        let message = `✅ Successfully imported ${importedCount} product${importedCount > 1 ? 's' : ''} into your inventory!`;\n        if (failedCount > 0) {\n          message += `\\n\\n⚠️ ${failedCount} product${failedCount > 1 ? 's' : ''} failed to import:`;\n          message += `\\n${failedProducts.slice(0, 5).join('\\n')}`;\n          if (failedProducts.length > 5) {\n            message += `\\n... and ${failedProducts.length - 5} more`;\n          }\n        }\n        alert(message);\n      } else {\n        alert('❌ No products were imported. Please check the data and try again.');\n      }\n\n      // Reset the form\n      setImportFile(null);\n      setImportResult(null);\n      setShowImportPreview(false);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Import error:', error);\n      alert('❌ Error importing products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle template download\n  const handleDownloadTemplate = () => {\n    generateImportTemplate();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), \"Bulk Operations (\", selectedProducts.length, \" products selected)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), !operation ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setOperation('update'),\n          className: \"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",\n          children: [/*#__PURE__*/_jsxDEV(Edit, {\n            className: \"h-6 w-6 text-blue-600 mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900\",\n            children: \"Bulk Update\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Update multiple products at once\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setOperation('delete'),\n          className: \"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",\n          children: [/*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"h-6 w-6 text-red-600 mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900\",\n            children: \"Bulk Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Delete selected products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setOperation('export'),\n          className: \"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            className: \"h-6 w-6 text-green-600 mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900\",\n            children: \"Export\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Export to CSV file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setOperation('import'),\n          className: \"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\",\n          children: [/*#__PURE__*/_jsxDEV(Upload, {\n            className: \"h-6 w-6 text-purple-600 mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900\",\n            children: \"Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Import from Excel or CSV file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this) : operation === 'update' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-medium text-gray-900\",\n          children: \"Bulk Update Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Category (leave empty to keep current)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: bulkUpdateData.category,\n              onChange: e => setBulkUpdateData(prev => ({\n                ...prev,\n                category: e.target.value\n              })),\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2\",\n              placeholder: \"New category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Price Adjustment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: bulkUpdateData.adjustmentType,\n                onChange: e => setBulkUpdateData(prev => ({\n                  ...prev,\n                  adjustmentType: e.target.value\n                })),\n                className: \"border border-gray-300 rounded-l-md px-3 py-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"percentage\",\n                  children: \"%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fixed\",\n                  children: \"KSh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: bulkUpdateData.priceAdjustment,\n                onChange: e => setBulkUpdateData(prev => ({\n                  ...prev,\n                  priceAdjustment: e.target.value\n                })),\n                className: \"flex-1 border border-gray-300 rounded-r-md px-3 py-2\",\n                placeholder: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Stock Adjustment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: bulkUpdateData.stockAdjustment,\n              onChange: e => setBulkUpdateData(prev => ({\n                ...prev,\n                stockAdjustment: e.target.value\n              })),\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2\",\n              placeholder: \"+/- quantity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Reorder Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: bulkUpdateData.reorderLevel,\n              onChange: e => setBulkUpdateData(prev => ({\n                ...prev,\n                reorderLevel: e.target.value\n              })),\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2\",\n              placeholder: \"New reorder level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Active Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: bulkUpdateData.isActive,\n              onChange: e => setBulkUpdateData(prev => ({\n                ...prev,\n                isActive: e.target.value\n              })),\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Keep current\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setOperation(null),\n            className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBulkUpdate,\n            disabled: processing,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n            children: processing ? 'Updating...' : 'Update Products'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this) : operation === 'delete' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-red-600\",\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium\",\n            children: \"Delete Selected Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"You are about to delete \", selectedProducts.length, \" products. This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-800\",\n            children: \"Products to be deleted:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mt-2 text-sm text-red-700 max-h-32 overflow-y-auto\",\n            children: [selectedProducts.slice(0, 10).map(product => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2022 \", product.name]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 19\n            }, this)), selectedProducts.length > 10 && /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2022 ... and \", selectedProducts.length - 10, \" more\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setOperation(null),\n            className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBulkDelete,\n            disabled: processing,\n            className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50\",\n            children: processing ? 'Deleting...' : 'Delete Products'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 11\n      }, this) : operation === 'export' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-green-600\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium\",\n            children: \"Export Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"Export \", selectedProducts.length, \" selected products to a CSV file.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setOperation(null),\n            className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleExport,\n            className: \"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\",\n            children: \"Export to CSV\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 11\n      }, this) : operation === 'import' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center text-purple-600\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"h-5 w-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium\",\n              children: \"Import Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleDownloadTemplate,\n            className: \"flex items-center text-sm text-blue-600 hover:text-blue-800\",\n            children: [/*#__PURE__*/_jsxDEV(FileSpreadsheet, {\n              className: \"h-4 w-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this), \"Download Template\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 border border-blue-200 rounded-md p-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-800\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Supported formats:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this), \" Excel (.xlsx, .xls) and CSV (.csv) files\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-700 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Required columns:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), \" Name, Category, Price, Stock Quantity\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Optional columns:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this), \" Description, Reorder Level, Has Expiry, Expiry Date, Is Active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this), !showImportPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"file-input\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Select File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"file-input\",\n              type: \"file\",\n              accept: \".csv,.xlsx,.xls\",\n              onChange: e => {\n                var _e$target$files;\n                return handleFileSelect(((_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0]) || null);\n              },\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 19\n            }, this), processing && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-2\",\n              children: \"Parsing file...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 17\n          }, this), importFile && !processing && importResult && !importResult.success && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-red-800\",\n              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"File validation failed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 21\n            }, this), importResult.errors && /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-sm text-red-700 mt-1 list-disc list-inside\",\n              children: importResult.errors.map((error, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: error\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 27\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-50 border border-green-200 rounded-md p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-green-800\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Import Preview Ready\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full\",\n                children: [(importResult === null || importResult === void 0 ? void 0 : (_importResult$data = importResult.data) === null || _importResult$data === void 0 ? void 0 : _importResult$data.length) || 0, \" products\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-green-700 mt-2\",\n              children: \"Review the products below and click \\\"Execute Import\\\" to add them to your inventory.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this), (importResult === null || importResult === void 0 ? void 0 : importResult.warnings) && importResult.warnings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 bg-yellow-50 border border-yellow-200 rounded-md p-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-yellow-800 font-medium flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                  className: \"h-4 w-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 25\n                }, this), \"Warnings:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-sm text-yellow-700 mt-1 list-disc list-inside ml-5\",\n                children: importResult.warnings.map((warning, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: warning\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white border border-gray-200 rounded-lg overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 px-4 py-2 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Products to Import (Preview)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-64 overflow-y-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"min-w-full text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50 sticky top-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-2 text-left font-medium text-gray-700\",\n                      children: \"#\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-2 text-left font-medium text-gray-700\",\n                      children: \"Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-2 text-left font-medium text-gray-700\",\n                      children: \"Category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-2 text-left font-medium text-gray-700\",\n                      children: \"Price\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-2 text-left font-medium text-gray-700\",\n                      children: \"Stock\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [importResult === null || importResult === void 0 ? void 0 : (_importResult$data2 = importResult.data) === null || _importResult$data2 === void 0 ? void 0 : _importResult$data2.slice(0, 15).map((product, index) => {\n                    var _product$price;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"border-t border-gray-200 hover:bg-gray-50\",\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-4 py-2 text-gray-500\",\n                        children: index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-4 py-2 font-medium\",\n                        children: product.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-4 py-2\",\n                        children: product.category\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 597,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-4 py-2 text-green-600 font-medium\",\n                        children: [\"KSh \", (_product$price = product.price) === null || _product$price === void 0 ? void 0 : _product$price.toLocaleString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-4 py-2\",\n                        children: product.stockQuantity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 599,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 27\n                    }, this);\n                  }), ((importResult === null || importResult === void 0 ? void 0 : (_importResult$data3 = importResult.data) === null || _importResult$data3 === void 0 ? void 0 : _importResult$data3.length) || 0) > 15 && /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"border-t border-gray-200 bg-gray-50\",\n                    children: /*#__PURE__*/_jsxDEV(\"td\", {\n                      colSpan: 5,\n                      className: \"px-4 py-3 text-center text-gray-500 font-medium\",\n                      children: [\"... and \", ((importResult === null || importResult === void 0 ? void 0 : (_importResult$data4 = importResult.data) === null || _importResult$data4 === void 0 ? void 0 : _importResult$data4.length) || 0) - 15, \" more products\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setOperation(null);\n              setImportFile(null);\n              setImportResult(null);\n              setShowImportPreview(false);\n            },\n            className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [!showImportPreview && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleDownloadTemplate,\n              className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FileSpreadsheet, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 21\n              }, this), \"Download Template\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 19\n            }, this), showImportPreview && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setImportFile(null);\n                  setImportResult(null);\n                  setShowImportPreview(false);\n                },\n                className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Choose Different File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleConfirmImport,\n                disabled: processing || !(importResult !== null && importResult !== void 0 && importResult.data) || importResult.data.length === 0,\n                className: \"px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 23\n                }, this), processing ? 'Executing Import...' : `Execute Import (${(importResult === null || importResult === void 0 ? void 0 : (_importResult$data5 = importResult.data) === null || _importResult$data5 === void 0 ? void 0 : _importResult$data5.length) || 0} products)`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s(BulkOperations, \"wN1sz9LlliOkgKlRid+ZtZjlYCc=\", false, function () {\n  return [useProducts];\n});\n_c = BulkOperations;\nexport default BulkOperations;\nvar _c;\n$RefreshReg$(_c, \"BulkOperations\");", "map": {"version": 3, "names": ["React", "useState", "Upload", "Download", "Edit", "Trash2", "Package", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "X", "FileSpreadsheet", "useProducts", "parseImportFile", "generateImportTemplate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BulkOperations", "selectedProducts", "onClose", "onClearSelection", "_s", "_importResult$data", "_importResult$data2", "_importResult$data3", "_importResult$data4", "_importResult$data5", "updateProduct", "deleteProduct", "createProduct", "operation", "setOperation", "bulkUpdateData", "setBulkUpdateData", "category", "priceAdjustment", "adjustmentType", "stockAdjustment", "reorderLevel", "isActive", "processing", "setProcessing", "importFile", "setImportFile", "importResult", "setImportResult", "showImportPreview", "setShowImportPreview", "handleBulkUpdate", "length", "product", "updates", "adjustment", "parseFloat", "price", "parseInt", "stockQuantity", "Math", "max", "Object", "keys", "id", "alert", "error", "console", "handleBulkDelete", "confirmed", "window", "confirm", "handleExport", "csvHeaders", "csvData", "map", "_product$expiryDate", "name", "description", "hasEx<PERSON>ry", "expiryDate", "toISOString", "createdAt", "csv<PERSON><PERSON>nt", "row", "field", "join", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "Date", "split", "click", "revokeObjectURL", "handleFileSelect", "file", "result", "success", "data", "errors", "handleConfirmImport", "totalProducts", "confirmMessage", "importedCount", "failedCount", "failedProducts", "productData", "push", "message", "slice", "handleDownloadTemplate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "e", "prev", "target", "placeholder", "disabled", "htmlFor", "accept", "_e$target$files", "files", "index", "warnings", "warning", "_product$price", "toLocaleString", "colSpan", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/BulkOperations.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Upload,\n  Download,\n  Edit,\n  Trash2,\n  Package,\n  AlertTriangle,\n  CheckCircle,\n  X,\n  FileSpreadsheet\n} from 'lucide-react';\nimport { Product } from '../../types';\nimport { useProducts } from '../../hooks/useProducts';\nimport { parseImportFile, generateImportTemplate, ImportResult } from '../../utils/excelImport';\n\ninterface BulkOperationsProps {\n  selectedProducts: Product[];\n  onClose: () => void;\n  onClearSelection: () => void;\n}\n\nconst BulkOperations: React.FC<BulkOperationsProps> = ({\n  selectedProducts,\n  onClose,\n  onClearSelection\n}) => {\n  const { updateProduct, deleteProduct, createProduct } = useProducts();\n  const [operation, setOperation] = useState<'update' | 'delete' | 'export' | 'import' | null>(null);\n  const [bulkUpdateData, setBulkUpdateData] = useState({\n    category: '',\n    priceAdjustment: '',\n    adjustmentType: 'percentage' as 'percentage' | 'fixed',\n    stockAdjustment: '',\n    reorderLevel: '',\n    isActive: ''\n  });\n  const [processing, setProcessing] = useState(false);\n  const [importFile, setImportFile] = useState<File | null>(null);\n  const [importResult, setImportResult] = useState<ImportResult | null>(null);\n  const [showImportPreview, setShowImportPreview] = useState(false);\n\n  // Handle bulk price update\n  const handleBulkUpdate = async () => {\n    if (selectedProducts.length === 0) return;\n\n    setProcessing(true);\n    try {\n      for (const product of selectedProducts) {\n        const updates: Partial<Product> = {};\n\n        // Category update\n        if (bulkUpdateData.category) {\n          updates.category = bulkUpdateData.category;\n        }\n\n        // Price adjustment\n        if (bulkUpdateData.priceAdjustment) {\n          const adjustment = parseFloat(bulkUpdateData.priceAdjustment);\n          if (bulkUpdateData.adjustmentType === 'percentage') {\n            updates.price = product.price * (1 + adjustment / 100);\n          } else {\n            updates.price = product.price + adjustment;\n          }\n        }\n\n        // Stock adjustment\n        if (bulkUpdateData.stockAdjustment) {\n          const adjustment = parseInt(bulkUpdateData.stockAdjustment);\n          updates.stockQuantity = Math.max(0, product.stockQuantity + adjustment);\n        }\n\n        // Reorder level\n        if (bulkUpdateData.reorderLevel) {\n          updates.reorderLevel = parseInt(bulkUpdateData.reorderLevel);\n        }\n\n        // Active status\n        if (bulkUpdateData.isActive !== '') {\n          updates.isActive = bulkUpdateData.isActive === 'true';\n        }\n\n        if (Object.keys(updates).length > 0) {\n          await updateProduct(product.id, updates);\n        }\n      }\n\n      alert(`Successfully updated ${selectedProducts.length} products`);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Bulk update error:', error);\n      alert('Error updating products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle bulk delete\n  const handleBulkDelete = async () => {\n    if (selectedProducts.length === 0) return;\n\n    const confirmed = window.confirm(\n      `Are you sure you want to delete ${selectedProducts.length} products? This action cannot be undone.`\n    );\n\n    if (!confirmed) return;\n\n    setProcessing(true);\n    try {\n      for (const product of selectedProducts) {\n        await deleteProduct(product.id);\n      }\n\n      alert(`Successfully deleted ${selectedProducts.length} products`);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Bulk delete error:', error);\n      alert('Error deleting products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Export products to CSV\n  const handleExport = () => {\n    const csvHeaders = [\n      'ID',\n      'Name',\n      'Description',\n      'Category',\n      'Price',\n      'Stock Quantity',\n      'Reorder Level',\n      'Has Expiry',\n      'Expiry Date',\n      'Is Active',\n      'Created At'\n    ];\n\n    const csvData = selectedProducts.map(product => [\n      product.id,\n      product.name,\n      product.description,\n      product.category,\n      product.price,\n      product.stockQuantity,\n      product.reorderLevel,\n      product.hasExpiry,\n      product.expiryDate?.toISOString() || '',\n      product.isActive,\n      product.createdAt.toISOString()\n    ]);\n\n    const csvContent = [csvHeaders, ...csvData]\n      .map(row => row.map(field => `\"${field}\"`).join(','))\n      .join('\\n');\n\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `products-export-${new Date().toISOString().split('T')[0]}.csv`;\n    link.click();\n    window.URL.revokeObjectURL(url);\n\n    alert(`Exported ${selectedProducts.length} products to CSV`);\n  };\n\n  // Handle file parsing and preview\n  const handleFileSelect = async (file: File | null) => {\n    setImportFile(file);\n    setImportResult(null);\n    setShowImportPreview(false);\n\n    if (!file) return;\n\n    setProcessing(true);\n    try {\n      const result = await parseImportFile(file);\n      setImportResult(result);\n\n      if (result.success && result.data && result.data.length > 0) {\n        setShowImportPreview(true);\n      } else if (result.errors) {\n        alert(`Import validation failed:\\n${result.errors.join('\\n')}`);\n      }\n    } catch (error) {\n      console.error('File parsing error:', error);\n      alert('Error parsing file. Please check the file format.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle actual import after preview confirmation\n  const handleConfirmImport = async () => {\n    if (!importResult || !importResult.data) return;\n\n    // Show confirmation dialog\n    const totalProducts = importResult.data.length;\n    const confirmMessage = `Are you sure you want to import ${totalProducts} product${totalProducts > 1 ? 's' : ''} into your inventory?\\n\\nThis action cannot be undone.`;\n\n    if (!window.confirm(confirmMessage)) {\n      return;\n    }\n\n    setProcessing(true);\n    try {\n      let importedCount = 0;\n      let failedCount = 0;\n      const failedProducts: string[] = [];\n\n      for (const productData of importResult.data) {\n        try {\n          await createProduct(productData as Omit<Product, 'id' | 'createdAt' | 'updatedAt'>);\n          importedCount++;\n        } catch (error) {\n          console.error('Error creating product:', error);\n          failedCount++;\n          failedProducts.push(productData.name || 'Unknown product');\n        }\n      }\n\n      // Show detailed results\n      if (importedCount > 0) {\n        let message = `✅ Successfully imported ${importedCount} product${importedCount > 1 ? 's' : ''} into your inventory!`;\n\n        if (failedCount > 0) {\n          message += `\\n\\n⚠️ ${failedCount} product${failedCount > 1 ? 's' : ''} failed to import:`;\n          message += `\\n${failedProducts.slice(0, 5).join('\\n')}`;\n          if (failedProducts.length > 5) {\n            message += `\\n... and ${failedProducts.length - 5} more`;\n          }\n        }\n\n        alert(message);\n      } else {\n        alert('❌ No products were imported. Please check the data and try again.');\n      }\n\n      // Reset the form\n      setImportFile(null);\n      setImportResult(null);\n      setShowImportPreview(false);\n      onClearSelection();\n      onClose();\n    } catch (error) {\n      console.error('Import error:', error);\n      alert('❌ Error importing products. Please try again.');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // Handle template download\n  const handleDownloadTemplate = () => {\n    generateImportTemplate();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n            <Package className=\"h-5 w-5 mr-2\" />\n            Bulk Operations ({selectedProducts.length} products selected)\n          </h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {!operation ? (\n          <div className=\"grid grid-cols-2 gap-4\">\n            <button\n              onClick={() => setOperation('update')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Edit className=\"h-6 w-6 text-blue-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Bulk Update</h4>\n              <p className=\"text-sm text-gray-500\">Update multiple products at once</p>\n            </button>\n\n            <button\n              onClick={() => setOperation('delete')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Trash2 className=\"h-6 w-6 text-red-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Bulk Delete</h4>\n              <p className=\"text-sm text-gray-500\">Delete selected products</p>\n            </button>\n\n            <button\n              onClick={() => setOperation('export')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Download className=\"h-6 w-6 text-green-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Export</h4>\n              <p className=\"text-sm text-gray-500\">Export to CSV file</p>\n            </button>\n\n            <button\n              onClick={() => setOperation('import')}\n              className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\"\n            >\n              <Upload className=\"h-6 w-6 text-purple-600 mb-2\" />\n              <h4 className=\"font-medium text-gray-900\">Import</h4>\n              <p className=\"text-sm text-gray-500\">Import from Excel or CSV file</p>\n            </button>\n          </div>\n        ) : operation === 'update' ? (\n          <div className=\"space-y-4\">\n            <h4 className=\"font-medium text-gray-900\">Bulk Update Products</h4>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category (leave empty to keep current)\n                </label>\n                <input\n                  type=\"text\"\n                  value={bulkUpdateData.category}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, category: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  placeholder=\"New category\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Price Adjustment\n                </label>\n                <div className=\"flex\">\n                  <select\n                    value={bulkUpdateData.adjustmentType}\n                    onChange={(e) => setBulkUpdateData(prev => ({ \n                      ...prev, \n                      adjustmentType: e.target.value as 'percentage' | 'fixed' \n                    }))}\n                    className=\"border border-gray-300 rounded-l-md px-3 py-2\"\n                  >\n                    <option value=\"percentage\">%</option>\n                    <option value=\"fixed\">KSh</option>\n                  </select>\n                  <input\n                    type=\"number\"\n                    value={bulkUpdateData.priceAdjustment}\n                    onChange={(e) => setBulkUpdateData(prev => ({ ...prev, priceAdjustment: e.target.value }))}\n                    className=\"flex-1 border border-gray-300 rounded-r-md px-3 py-2\"\n                    placeholder=\"0\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Stock Adjustment\n                </label>\n                <input\n                  type=\"number\"\n                  value={bulkUpdateData.stockAdjustment}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, stockAdjustment: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  placeholder=\"+/- quantity\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Reorder Level\n                </label>\n                <input\n                  type=\"number\"\n                  value={bulkUpdateData.reorderLevel}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, reorderLevel: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  placeholder=\"New reorder level\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Active Status\n                </label>\n                <select\n                  value={bulkUpdateData.isActive}\n                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, isActive: e.target.value }))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                >\n                  <option value=\"\">Keep current</option>\n                  <option value=\"true\">Active</option>\n                  <option value=\"false\">Inactive</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                onClick={() => setOperation(null)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n              <button\n                onClick={handleBulkUpdate}\n                disabled={processing}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {processing ? 'Updating...' : 'Update Products'}\n              </button>\n            </div>\n          </div>\n        ) : operation === 'delete' ? (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center text-red-600\">\n              <AlertTriangle className=\"h-5 w-5 mr-2\" />\n              <h4 className=\"font-medium\">Delete Selected Products</h4>\n            </div>\n            \n            <p className=\"text-gray-600\">\n              You are about to delete {selectedProducts.length} products. This action cannot be undone.\n            </p>\n\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n              <p className=\"text-sm text-red-800\">\n                Products to be deleted:\n              </p>\n              <ul className=\"mt-2 text-sm text-red-700 max-h-32 overflow-y-auto\">\n                {selectedProducts.slice(0, 10).map(product => (\n                  <li key={product.id}>• {product.name}</li>\n                ))}\n                {selectedProducts.length > 10 && (\n                  <li>• ... and {selectedProducts.length - 10} more</li>\n                )}\n              </ul>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                onClick={() => setOperation(null)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n              <button\n                onClick={handleBulkDelete}\n                disabled={processing}\n                className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50\"\n              >\n                {processing ? 'Deleting...' : 'Delete Products'}\n              </button>\n            </div>\n          </div>\n        ) : operation === 'export' ? (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center text-green-600\">\n              <Download className=\"h-5 w-5 mr-2\" />\n              <h4 className=\"font-medium\">Export Products</h4>\n            </div>\n            \n            <p className=\"text-gray-600\">\n              Export {selectedProducts.length} selected products to a CSV file.\n            </p>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                onClick={() => setOperation(null)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n              <button\n                onClick={handleExport}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\"\n              >\n                Export to CSV\n              </button>\n            </div>\n          </div>\n        ) : operation === 'import' ? (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center text-purple-600\">\n                <Upload className=\"h-5 w-5 mr-2\" />\n                <h4 className=\"font-medium\">Import Products</h4>\n              </div>\n              <button\n                onClick={handleDownloadTemplate}\n                className=\"flex items-center text-sm text-blue-600 hover:text-blue-800\"\n              >\n                <FileSpreadsheet className=\"h-4 w-4 mr-1\" />\n                Download Template\n              </button>\n            </div>\n\n            <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3\">\n              <p className=\"text-sm text-blue-800\">\n                <strong>Supported formats:</strong> Excel (.xlsx, .xls) and CSV (.csv) files\n              </p>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                <strong>Required columns:</strong> Name, Category, Price, Stock Quantity\n              </p>\n              <p className=\"text-sm text-blue-700\">\n                <strong>Optional columns:</strong> Description, Reorder Level, Has Expiry, Expiry Date, Is Active\n              </p>\n            </div>\n\n            {!showImportPreview ? (\n              <div className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"file-input\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Select File\n                  </label>\n                  <input\n                    id=\"file-input\"\n                    type=\"file\"\n                    accept=\".csv,.xlsx,.xls\"\n                    onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  />\n                  {processing && (\n                    <p className=\"text-sm text-gray-600 mt-2\">Parsing file...</p>\n                  )}\n                </div>\n\n                {importFile && !processing && importResult && !importResult.success && (\n                  <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                    <div className=\"flex items-center text-red-800\">\n                      <AlertTriangle className=\"h-4 w-4 mr-2\" />\n                      <span className=\"font-medium\">File validation failed</span>\n                    </div>\n                    {importResult.errors && (\n                      <ul className=\"text-sm text-red-700 mt-1 list-disc list-inside\">\n                        {importResult.errors.map((error, index) => (\n                          <li key={index}>{error}</li>\n                        ))}\n                      </ul>\n                    )}\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                <div className=\"bg-green-50 border border-green-200 rounded-md p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center text-green-800\">\n                      <CheckCircle className=\"h-5 w-5 mr-2\" />\n                      <span className=\"font-medium\">Import Preview Ready</span>\n                    </div>\n                    <span className=\"bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full\">\n                      {importResult?.data?.length || 0} products\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-green-700 mt-2\">\n                    Review the products below and click \"Execute Import\" to add them to your inventory.\n                  </p>\n                  {importResult?.warnings && importResult.warnings.length > 0 && (\n                    <div className=\"mt-3 bg-yellow-50 border border-yellow-200 rounded-md p-2\">\n                      <p className=\"text-sm text-yellow-800 font-medium flex items-center\">\n                        <AlertTriangle className=\"h-4 w-4 mr-1\" />\n                        Warnings:\n                      </p>\n                      <ul className=\"text-sm text-yellow-700 mt-1 list-disc list-inside ml-5\">\n                        {importResult.warnings.map((warning, index) => (\n                          <li key={index}>{warning}</li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\n                  <div className=\"bg-gray-50 px-4 py-2 border-b border-gray-200\">\n                    <h5 className=\"text-sm font-medium text-gray-900\">Products to Import (Preview)</h5>\n                  </div>\n                  <div className=\"max-h-64 overflow-y-auto\">\n                    <table className=\"min-w-full text-sm\">\n                      <thead className=\"bg-gray-50 sticky top-0\">\n                        <tr>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">#</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Name</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Category</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Price</th>\n                          <th className=\"px-4 py-2 text-left font-medium text-gray-700\">Stock</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {importResult?.data?.slice(0, 15).map((product, index) => (\n                          <tr key={index} className=\"border-t border-gray-200 hover:bg-gray-50\">\n                            <td className=\"px-4 py-2 text-gray-500\">{index + 1}</td>\n                            <td className=\"px-4 py-2 font-medium\">{product.name}</td>\n                            <td className=\"px-4 py-2\">{product.category}</td>\n                            <td className=\"px-4 py-2 text-green-600 font-medium\">KSh {product.price?.toLocaleString()}</td>\n                            <td className=\"px-4 py-2\">{product.stockQuantity}</td>\n                          </tr>\n                        ))}\n                        {(importResult?.data?.length || 0) > 15 && (\n                          <tr className=\"border-t border-gray-200 bg-gray-50\">\n                            <td colSpan={5} className=\"px-4 py-3 text-center text-gray-500 font-medium\">\n                              ... and {(importResult?.data?.length || 0) - 15} more products\n                            </td>\n                          </tr>\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex justify-between items-center pt-4\">\n              <button\n                onClick={() => {\n                  setOperation(null);\n                  setImportFile(null);\n                  setImportResult(null);\n                  setShowImportPreview(false);\n                }}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Back\n              </button>\n\n              <div className=\"flex space-x-3\">\n                {!showImportPreview && (\n                  <button\n                    onClick={handleDownloadTemplate}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center\"\n                  >\n                    <FileSpreadsheet className=\"h-4 w-4 mr-2\" />\n                    Download Template\n                  </button>\n                )}\n\n                {showImportPreview && (\n                  <>\n                    <button\n                      onClick={() => {\n                        setImportFile(null);\n                        setImportResult(null);\n                        setShowImportPreview(false);\n                      }}\n                      className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                    >\n                      Choose Different File\n                    </button>\n                    <button\n                      onClick={handleConfirmImport}\n                      disabled={processing || !importResult?.data || importResult.data.length === 0}\n                      className=\"px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium\"\n                    >\n                      <Upload className=\"h-4 w-4 mr-2\" />\n                      {processing ? 'Executing Import...' : `Execute Import (${importResult?.data?.length || 0} products)`}\n                    </button>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n    </div>\n  );\n};\n\nexport default BulkOperations;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,WAAW,EACXC,CAAC,EACDC,eAAe,QACV,cAAc;AAErB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,eAAe,EAAEC,sBAAsB,QAAsB,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQhG,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,gBAAgB;EAChBC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;EACJ,MAAM;IAAEC,aAAa;IAAEC,aAAa;IAAEC;EAAc,CAAC,GAAGnB,WAAW,CAAC,CAAC;EACrE,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAmD,IAAI,CAAC;EAClG,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC;IACnDkC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,YAAsC;IACtDC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAc,IAAI,CAAC;EAC/D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAMgD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI9B,gBAAgB,CAAC+B,MAAM,KAAK,CAAC,EAAE;IAEnCR,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,KAAK,MAAMS,OAAO,IAAIhC,gBAAgB,EAAE;QACtC,MAAMiC,OAAyB,GAAG,CAAC,CAAC;;QAEpC;QACA,IAAInB,cAAc,CAACE,QAAQ,EAAE;UAC3BiB,OAAO,CAACjB,QAAQ,GAAGF,cAAc,CAACE,QAAQ;QAC5C;;QAEA;QACA,IAAIF,cAAc,CAACG,eAAe,EAAE;UAClC,MAAMiB,UAAU,GAAGC,UAAU,CAACrB,cAAc,CAACG,eAAe,CAAC;UAC7D,IAAIH,cAAc,CAACI,cAAc,KAAK,YAAY,EAAE;YAClDe,OAAO,CAACG,KAAK,GAAGJ,OAAO,CAACI,KAAK,IAAI,CAAC,GAAGF,UAAU,GAAG,GAAG,CAAC;UACxD,CAAC,MAAM;YACLD,OAAO,CAACG,KAAK,GAAGJ,OAAO,CAACI,KAAK,GAAGF,UAAU;UAC5C;QACF;;QAEA;QACA,IAAIpB,cAAc,CAACK,eAAe,EAAE;UAClC,MAAMe,UAAU,GAAGG,QAAQ,CAACvB,cAAc,CAACK,eAAe,CAAC;UAC3Dc,OAAO,CAACK,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,OAAO,CAACM,aAAa,GAAGJ,UAAU,CAAC;QACzE;;QAEA;QACA,IAAIpB,cAAc,CAACM,YAAY,EAAE;UAC/Ba,OAAO,CAACb,YAAY,GAAGiB,QAAQ,CAACvB,cAAc,CAACM,YAAY,CAAC;QAC9D;;QAEA;QACA,IAAIN,cAAc,CAACO,QAAQ,KAAK,EAAE,EAAE;UAClCY,OAAO,CAACZ,QAAQ,GAAGP,cAAc,CAACO,QAAQ,KAAK,MAAM;QACvD;QAEA,IAAIoB,MAAM,CAACC,IAAI,CAACT,OAAO,CAAC,CAACF,MAAM,GAAG,CAAC,EAAE;UACnC,MAAMtB,aAAa,CAACuB,OAAO,CAACW,EAAE,EAAEV,OAAO,CAAC;QAC1C;MACF;MAEAW,KAAK,CAAC,wBAAwB5C,gBAAgB,CAAC+B,MAAM,WAAW,CAAC;MACjE7B,gBAAgB,CAAC,CAAC;MAClBD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CD,KAAK,CAAC,4CAA4C,CAAC;IACrD,CAAC,SAAS;MACRrB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMwB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI/C,gBAAgB,CAAC+B,MAAM,KAAK,CAAC,EAAE;IAEnC,MAAMiB,SAAS,GAAGC,MAAM,CAACC,OAAO,CAC9B,mCAAmClD,gBAAgB,CAAC+B,MAAM,0CAC5D,CAAC;IAED,IAAI,CAACiB,SAAS,EAAE;IAEhBzB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,KAAK,MAAMS,OAAO,IAAIhC,gBAAgB,EAAE;QACtC,MAAMU,aAAa,CAACsB,OAAO,CAACW,EAAE,CAAC;MACjC;MAEAC,KAAK,CAAC,wBAAwB5C,gBAAgB,CAAC+B,MAAM,WAAW,CAAC;MACjE7B,gBAAgB,CAAC,CAAC;MAClBD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CD,KAAK,CAAC,4CAA4C,CAAC;IACrD,CAAC,SAAS;MACRrB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG,CACjB,IAAI,EACJ,MAAM,EACN,aAAa,EACb,UAAU,EACV,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,aAAa,EACb,WAAW,EACX,YAAY,CACb;IAED,MAAMC,OAAO,GAAGrD,gBAAgB,CAACsD,GAAG,CAACtB,OAAO;MAAA,IAAAuB,mBAAA;MAAA,OAAI,CAC9CvB,OAAO,CAACW,EAAE,EACVX,OAAO,CAACwB,IAAI,EACZxB,OAAO,CAACyB,WAAW,EACnBzB,OAAO,CAAChB,QAAQ,EAChBgB,OAAO,CAACI,KAAK,EACbJ,OAAO,CAACM,aAAa,EACrBN,OAAO,CAACZ,YAAY,EACpBY,OAAO,CAAC0B,SAAS,EACjB,EAAAH,mBAAA,GAAAvB,OAAO,CAAC2B,UAAU,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBK,WAAW,CAAC,CAAC,KAAI,EAAE,EACvC5B,OAAO,CAACX,QAAQ,EAChBW,OAAO,CAAC6B,SAAS,CAACD,WAAW,CAAC,CAAC,CAChC;IAAA,EAAC;IAEF,MAAME,UAAU,GAAG,CAACV,UAAU,EAAE,GAAGC,OAAO,CAAC,CACxCC,GAAG,CAACS,GAAG,IAAIA,GAAG,CAACT,GAAG,CAACU,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CACpDA,IAAI,CAAC,IAAI,CAAC;IAEb,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;MAAEM,IAAI,EAAE;IAAW,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGpB,MAAM,CAACqB,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,mBAAmB,IAAIC,IAAI,CAAC,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC/EN,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ9B,MAAM,CAACqB,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;IAE/BzB,KAAK,CAAC,YAAY5C,gBAAgB,CAAC+B,MAAM,kBAAkB,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMkD,gBAAgB,GAAG,MAAOC,IAAiB,IAAK;IACpDzD,aAAa,CAACyD,IAAI,CAAC;IACnBvD,eAAe,CAAC,IAAI,CAAC;IACrBE,oBAAoB,CAAC,KAAK,CAAC;IAE3B,IAAI,CAACqD,IAAI,EAAE;IAEX3D,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAM4D,MAAM,GAAG,MAAM1F,eAAe,CAACyF,IAAI,CAAC;MAC1CvD,eAAe,CAACwD,MAAM,CAAC;MAEvB,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,IAAI,IAAIF,MAAM,CAACE,IAAI,CAACtD,MAAM,GAAG,CAAC,EAAE;QAC3DF,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM,IAAIsD,MAAM,CAACG,MAAM,EAAE;QACxB1C,KAAK,CAAC,8BAA8BuC,MAAM,CAACG,MAAM,CAACrB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MACjE;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CD,KAAK,CAAC,mDAAmD,CAAC;IAC5D,CAAC,SAAS;MACRrB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMgE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC7D,YAAY,IAAI,CAACA,YAAY,CAAC2D,IAAI,EAAE;;IAEzC;IACA,MAAMG,aAAa,GAAG9D,YAAY,CAAC2D,IAAI,CAACtD,MAAM;IAC9C,MAAM0D,cAAc,GAAG,mCAAmCD,aAAa,WAAWA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,wDAAwD;IAEtK,IAAI,CAACvC,MAAM,CAACC,OAAO,CAACuC,cAAc,CAAC,EAAE;MACnC;IACF;IAEAlE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,IAAImE,aAAa,GAAG,CAAC;MACrB,IAAIC,WAAW,GAAG,CAAC;MACnB,MAAMC,cAAwB,GAAG,EAAE;MAEnC,KAAK,MAAMC,WAAW,IAAInE,YAAY,CAAC2D,IAAI,EAAE;QAC3C,IAAI;UACF,MAAM1E,aAAa,CAACkF,WAA8D,CAAC;UACnFH,aAAa,EAAE;QACjB,CAAC,CAAC,OAAO7C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C8C,WAAW,EAAE;UACbC,cAAc,CAACE,IAAI,CAACD,WAAW,CAACrC,IAAI,IAAI,iBAAiB,CAAC;QAC5D;MACF;;MAEA;MACA,IAAIkC,aAAa,GAAG,CAAC,EAAE;QACrB,IAAIK,OAAO,GAAG,2BAA2BL,aAAa,WAAWA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,uBAAuB;QAEpH,IAAIC,WAAW,GAAG,CAAC,EAAE;UACnBI,OAAO,IAAI,UAAUJ,WAAW,WAAWA,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,oBAAoB;UACzFI,OAAO,IAAI,KAAKH,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/B,IAAI,CAAC,IAAI,CAAC,EAAE;UACvD,IAAI2B,cAAc,CAAC7D,MAAM,GAAG,CAAC,EAAE;YAC7BgE,OAAO,IAAI,aAAaH,cAAc,CAAC7D,MAAM,GAAG,CAAC,OAAO;UAC1D;QACF;QAEAa,KAAK,CAACmD,OAAO,CAAC;MAChB,CAAC,MAAM;QACLnD,KAAK,CAAC,mEAAmE,CAAC;MAC5E;;MAEA;MACAnB,aAAa,CAAC,IAAI,CAAC;MACnBE,eAAe,CAAC,IAAI,CAAC;MACrBE,oBAAoB,CAAC,KAAK,CAAC;MAC3B3B,gBAAgB,CAAC,CAAC;MAClBD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCD,KAAK,CAAC,+CAA+C,CAAC;IACxD,CAAC,SAAS;MACRrB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM0E,sBAAsB,GAAGA,CAAA,KAAM;IACnCvG,sBAAsB,CAAC,CAAC;EAC1B,CAAC;EAED,oBACEE,OAAA;IAAKsG,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFvG,OAAA;MAAKsG,SAAS,EAAC,mFAAmF;MAAAC,QAAA,gBAChGvG,OAAA;QAAKsG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDvG,OAAA;UAAIsG,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBACjEvG,OAAA,CAACT,OAAO;YAAC+G,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBACnB,EAACvG,gBAAgB,CAAC+B,MAAM,EAAC,qBAC5C;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3G,OAAA;UACE4G,OAAO,EAAEvG,OAAQ;UACjBiG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CvG,OAAA,CAACN,CAAC;YAAC4G,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL,CAAC3F,SAAS,gBACThB,OAAA;QAAKsG,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCvG,OAAA;UACE4G,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,QAAQ,CAAE;UACtCqF,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAE5EvG,OAAA,CAACX,IAAI;YAACiH,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C3G,OAAA;YAAIsG,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1D3G,OAAA;YAAGsG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAET3G,OAAA;UACE4G,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,QAAQ,CAAE;UACtCqF,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAE5EvG,OAAA,CAACV,MAAM;YAACgH,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChD3G,OAAA;YAAIsG,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1D3G,OAAA;YAAGsG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAET3G,OAAA;UACE4G,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,QAAQ,CAAE;UACtCqF,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAE5EvG,OAAA,CAACZ,QAAQ;YAACkH,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD3G,OAAA;YAAIsG,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD3G,OAAA;YAAGsG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAET3G,OAAA;UACE4G,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,QAAQ,CAAE;UACtCqF,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAE5EvG,OAAA,CAACb,MAAM;YAACmH,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnD3G,OAAA;YAAIsG,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD3G,OAAA;YAAGsG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ3F,SAAS,KAAK,QAAQ,gBACxBhB,OAAA;QAAKsG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvG,OAAA;UAAIsG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnE3G,OAAA;UAAKsG,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCvG,OAAA;YAAAuG,QAAA,gBACEvG,OAAA;cAAOsG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3G,OAAA;cACEwE,IAAI,EAAC,MAAM;cACXqC,KAAK,EAAE3F,cAAc,CAACE,QAAS;cAC/B0F,QAAQ,EAAGC,CAAC,IAAK5F,iBAAiB,CAAC6F,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5F,QAAQ,EAAE2F,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cACpFP,SAAS,EAAC,oDAAoD;cAC9DY,WAAW,EAAC;YAAc;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3G,OAAA;YAAAuG,QAAA,gBACEvG,OAAA;cAAOsG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3G,OAAA;cAAKsG,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBvG,OAAA;gBACE6G,KAAK,EAAE3F,cAAc,CAACI,cAAe;gBACrCwF,QAAQ,EAAGC,CAAC,IAAK5F,iBAAiB,CAAC6F,IAAI,KAAK;kBAC1C,GAAGA,IAAI;kBACP1F,cAAc,EAAEyF,CAAC,CAACE,MAAM,CAACJ;gBAC3B,CAAC,CAAC,CAAE;gBACJP,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAEzDvG,OAAA;kBAAQ6G,KAAK,EAAC,YAAY;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC3G,OAAA;kBAAQ6G,KAAK,EAAC,OAAO;kBAAAN,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACT3G,OAAA;gBACEwE,IAAI,EAAC,QAAQ;gBACbqC,KAAK,EAAE3F,cAAc,CAACG,eAAgB;gBACtCyF,QAAQ,EAAGC,CAAC,IAAK5F,iBAAiB,CAAC6F,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE3F,eAAe,EAAE0F,CAAC,CAACE,MAAM,CAACJ;gBAAM,CAAC,CAAC,CAAE;gBAC3FP,SAAS,EAAC,sDAAsD;gBAChEY,WAAW,EAAC;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3G,OAAA;YAAAuG,QAAA,gBACEvG,OAAA;cAAOsG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3G,OAAA;cACEwE,IAAI,EAAC,QAAQ;cACbqC,KAAK,EAAE3F,cAAc,CAACK,eAAgB;cACtCuF,QAAQ,EAAGC,CAAC,IAAK5F,iBAAiB,CAAC6F,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEzF,eAAe,EAAEwF,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cAC3FP,SAAS,EAAC,oDAAoD;cAC9DY,WAAW,EAAC;YAAc;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3G,OAAA;YAAAuG,QAAA,gBACEvG,OAAA;cAAOsG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3G,OAAA;cACEwE,IAAI,EAAC,QAAQ;cACbqC,KAAK,EAAE3F,cAAc,CAACM,YAAa;cACnCsF,QAAQ,EAAGC,CAAC,IAAK5F,iBAAiB,CAAC6F,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAExF,YAAY,EAAEuF,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cACxFP,SAAS,EAAC,oDAAoD;cAC9DY,WAAW,EAAC;YAAmB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3G,OAAA;YAAAuG,QAAA,gBACEvG,OAAA;cAAOsG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3G,OAAA;cACE6G,KAAK,EAAE3F,cAAc,CAACO,QAAS;cAC/BqF,QAAQ,EAAGC,CAAC,IAAK5F,iBAAiB,CAAC6F,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEvF,QAAQ,EAAEsF,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cACpFP,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBAE9DvG,OAAA;gBAAQ6G,KAAK,EAAC,EAAE;gBAAAN,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC3G,OAAA;gBAAQ6G,KAAK,EAAC,MAAM;gBAAAN,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC3G,OAAA;gBAAQ6G,KAAK,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3G,OAAA;UAAKsG,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CvG,OAAA;YACE4G,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,IAAI,CAAE;YAClCqF,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACvF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YACE4G,OAAO,EAAE1E,gBAAiB;YAC1BiF,QAAQ,EAAEzF,UAAW;YACrB4E,SAAS,EAAC,mFAAmF;YAAAC,QAAA,EAE5F7E,UAAU,GAAG,aAAa,GAAG;UAAiB;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ3F,SAAS,KAAK,QAAQ,gBACxBhB,OAAA;QAAKsG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvG,OAAA;UAAKsG,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7CvG,OAAA,CAACR,aAAa;YAAC8G,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C3G,OAAA;YAAIsG,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAEN3G,OAAA;UAAGsG,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,0BACH,EAACnG,gBAAgB,CAAC+B,MAAM,EAAC,0CACnD;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJ3G,OAAA;UAAKsG,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DvG,OAAA;YAAGsG,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3G,OAAA;YAAIsG,SAAS,EAAC,oDAAoD;YAAAC,QAAA,GAC/DnG,gBAAgB,CAACgG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC1C,GAAG,CAACtB,OAAO,iBACxCpC,OAAA;cAAAuG,QAAA,GAAqB,SAAE,EAACnE,OAAO,CAACwB,IAAI;YAAA,GAA3BxB,OAAO,CAACW,EAAE;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAsB,CAC1C,CAAC,EACDvG,gBAAgB,CAAC+B,MAAM,GAAG,EAAE,iBAC3BnC,OAAA;cAAAuG,QAAA,GAAI,iBAAU,EAACnG,gBAAgB,CAAC+B,MAAM,GAAG,EAAE,EAAC,OAAK;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN3G,OAAA;UAAKsG,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CvG,OAAA;YACE4G,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,IAAI,CAAE;YAClCqF,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACvF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YACE4G,OAAO,EAAEzD,gBAAiB;YAC1BgE,QAAQ,EAAEzF,UAAW;YACrB4E,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAE1F7E,UAAU,GAAG,aAAa,GAAG;UAAiB;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ3F,SAAS,KAAK,QAAQ,gBACxBhB,OAAA;QAAKsG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvG,OAAA;UAAKsG,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CvG,OAAA,CAACZ,QAAQ;YAACkH,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrC3G,OAAA;YAAIsG,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEN3G,OAAA;UAAGsG,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,SACpB,EAACnG,gBAAgB,CAAC+B,MAAM,EAAC,mCAClC;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJ3G,OAAA;UAAKsG,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CvG,OAAA;YACE4G,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,IAAI,CAAE;YAClCqF,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACvF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YACE4G,OAAO,EAAErD,YAAa;YACtB+C,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAC5E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ3F,SAAS,KAAK,QAAQ,gBACxBhB,OAAA;QAAKsG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvG,OAAA;UAAKsG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvG,OAAA;YAAKsG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDvG,OAAA,CAACb,MAAM;cAACmH,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC3G,OAAA;cAAIsG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN3G,OAAA;YACE4G,OAAO,EAAEP,sBAAuB;YAChCC,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAEvEvG,OAAA,CAACL,eAAe;cAAC2G,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3G,OAAA;UAAKsG,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/DvG,OAAA;YAAGsG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCvG,OAAA;cAAAuG,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6CACrC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3G,OAAA;YAAGsG,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACvCvG,OAAA;cAAAuG,QAAA,EAAQ;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,0CACpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3G,OAAA;YAAGsG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCvG,OAAA;cAAAuG,QAAA,EAAQ;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,mEACpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAEL,CAAC3E,iBAAiB,gBACjBhC,OAAA;UAAKsG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvG,OAAA;YAAAuG,QAAA,gBACEvG,OAAA;cAAOoH,OAAO,EAAC,YAAY;cAACd,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3G,OAAA;cACE+C,EAAE,EAAC,YAAY;cACfyB,IAAI,EAAC,MAAM;cACX6C,MAAM,EAAC,iBAAiB;cACxBP,QAAQ,EAAGC,CAAC;gBAAA,IAAAO,eAAA;gBAAA,OAAKjC,gBAAgB,CAAC,EAAAiC,eAAA,GAAAP,CAAC,CAACE,MAAM,CAACM,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC,KAAI,IAAI,CAAC;cAAA,CAAC;cAC/DhB,SAAS,EAAC;YAAoD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,EACDjF,UAAU,iBACT1B,OAAA;cAAGsG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC7D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL/E,UAAU,IAAI,CAACF,UAAU,IAAII,YAAY,IAAI,CAACA,YAAY,CAAC0D,OAAO,iBACjExF,OAAA;YAAKsG,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DvG,OAAA;cAAKsG,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CvG,OAAA,CAACR,aAAa;gBAAC8G,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1C3G,OAAA;gBAAMsG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,EACL7E,YAAY,CAAC4D,MAAM,iBAClB1F,OAAA;cAAIsG,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAC5DzE,YAAY,CAAC4D,MAAM,CAAChC,GAAG,CAAC,CAACT,KAAK,EAAEuE,KAAK,kBACpCxH,OAAA;gBAAAuG,QAAA,EAAiBtD;cAAK,GAAbuE,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN3G,OAAA;UAAKsG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvG,OAAA;YAAKsG,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjEvG,OAAA;cAAKsG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDvG,OAAA;gBAAKsG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CvG,OAAA,CAACP,WAAW;kBAAC6G,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxC3G,OAAA;kBAAMsG,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACN3G,OAAA;gBAAMsG,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,GACzF,CAAAzE,YAAY,aAAZA,YAAY,wBAAAtB,kBAAA,GAAZsB,YAAY,CAAE2D,IAAI,cAAAjF,kBAAA,uBAAlBA,kBAAA,CAAoB2B,MAAM,KAAI,CAAC,EAAC,WACnC;cAAA;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN3G,OAAA;cAAGsG,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAE3C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACH,CAAA7E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE2F,QAAQ,KAAI3F,YAAY,CAAC2F,QAAQ,CAACtF,MAAM,GAAG,CAAC,iBACzDnC,OAAA;cAAKsG,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEvG,OAAA;gBAAGsG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBAClEvG,OAAA,CAACR,aAAa;kBAAC8G,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ3G,OAAA;gBAAIsG,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EACpEzE,YAAY,CAAC2F,QAAQ,CAAC/D,GAAG,CAAC,CAACgE,OAAO,EAAEF,KAAK,kBACxCxH,OAAA;kBAAAuG,QAAA,EAAiBmB;gBAAO,GAAfF,KAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN3G,OAAA;YAAKsG,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzEvG,OAAA;cAAKsG,SAAS,EAAC,+CAA+C;cAAAC,QAAA,eAC5DvG,OAAA;gBAAIsG,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCvG,OAAA;gBAAOsG,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACnCvG,OAAA;kBAAOsG,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,eACxCvG,OAAA;oBAAAuG,QAAA,gBACEvG,OAAA;sBAAIsG,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpE3G,OAAA;sBAAIsG,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvE3G,OAAA;sBAAIsG,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3E3G,OAAA;sBAAIsG,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxE3G,OAAA;sBAAIsG,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR3G,OAAA;kBAAAuG,QAAA,GACGzE,YAAY,aAAZA,YAAY,wBAAArB,mBAAA,GAAZqB,YAAY,CAAE2D,IAAI,cAAAhF,mBAAA,uBAAlBA,mBAAA,CAAoB2F,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC1C,GAAG,CAAC,CAACtB,OAAO,EAAEoF,KAAK;oBAAA,IAAAG,cAAA;oBAAA,oBACnD3H,OAAA;sBAAgBsG,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,gBACnEvG,OAAA;wBAAIsG,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAEiB,KAAK,GAAG;sBAAC;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxD3G,OAAA;wBAAIsG,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEnE,OAAO,CAACwB;sBAAI;wBAAA4C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzD3G,OAAA;wBAAIsG,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAEnE,OAAO,CAAChB;sBAAQ;wBAAAoF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACjD3G,OAAA;wBAAIsG,SAAS,EAAC,sCAAsC;wBAAAC,QAAA,GAAC,MAAI,GAAAoB,cAAA,GAACvF,OAAO,CAACI,KAAK,cAAAmF,cAAA,uBAAbA,cAAA,CAAeC,cAAc,CAAC,CAAC;sBAAA;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC/F3G,OAAA;wBAAIsG,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAEnE,OAAO,CAACM;sBAAa;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GAL/Ca,KAAK;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMV,CAAC;kBAAA,CACN,CAAC,EACD,CAAC,CAAA7E,YAAY,aAAZA,YAAY,wBAAApB,mBAAA,GAAZoB,YAAY,CAAE2D,IAAI,cAAA/E,mBAAA,uBAAlBA,mBAAA,CAAoByB,MAAM,KAAI,CAAC,IAAI,EAAE,iBACrCnC,OAAA;oBAAIsG,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eACjDvG,OAAA;sBAAI6H,OAAO,EAAE,CAAE;sBAACvB,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,GAAC,UAClE,EAAC,CAAC,CAAAzE,YAAY,aAAZA,YAAY,wBAAAnB,mBAAA,GAAZmB,YAAY,CAAE2D,IAAI,cAAA9E,mBAAA,uBAAlBA,mBAAA,CAAoBwB,MAAM,KAAI,CAAC,IAAI,EAAE,EAAC,gBAClD;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED3G,OAAA;UAAKsG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDvG,OAAA;YACE4G,OAAO,EAAEA,CAAA,KAAM;cACb3F,YAAY,CAAC,IAAI,CAAC;cAClBY,aAAa,CAAC,IAAI,CAAC;cACnBE,eAAe,CAAC,IAAI,CAAC;cACrBE,oBAAoB,CAAC,KAAK,CAAC;YAC7B,CAAE;YACFqE,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACvF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3G,OAAA;YAAKsG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAC5B,CAACvE,iBAAiB,iBACjBhC,OAAA;cACE4G,OAAO,EAAEP,sBAAuB;cAChCC,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAE3FvG,OAAA,CAACL,eAAe;gBAAC2G,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAE9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEA3E,iBAAiB,iBAChBhC,OAAA,CAAAE,SAAA;cAAAqG,QAAA,gBACEvG,OAAA;gBACE4G,OAAO,EAAEA,CAAA,KAAM;kBACb/E,aAAa,CAAC,IAAI,CAAC;kBACnBE,eAAe,CAAC,IAAI,CAAC;kBACrBE,oBAAoB,CAAC,KAAK,CAAC;gBAC7B,CAAE;gBACFqE,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3G,OAAA;gBACE4G,OAAO,EAAEjB,mBAAoB;gBAC7BwB,QAAQ,EAAEzF,UAAU,IAAI,EAACI,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE2D,IAAI,KAAI3D,YAAY,CAAC2D,IAAI,CAACtD,MAAM,KAAK,CAAE;gBAC9EmE,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,gBAEzJvG,OAAA,CAACb,MAAM;kBAACmH,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAClCjF,UAAU,GAAG,qBAAqB,GAAG,mBAAmB,CAAAI,YAAY,aAAZA,YAAY,wBAAAlB,mBAAA,GAAZkB,YAAY,CAAE2D,IAAI,cAAA7E,mBAAA,uBAAlBA,mBAAA,CAAoBuB,MAAM,KAAI,CAAC,YAAY;cAAA;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC;YAAA,eACT,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpG,EAAA,CAtoBIJ,cAA6C;EAAA,QAKOP,WAAW;AAAA;AAAAkI,EAAA,GAL/D3H,cAA6C;AAwoBnD,eAAeA,cAAc;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}