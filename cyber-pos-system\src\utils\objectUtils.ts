/**
 * Utility function to remove undefined values from objects
 * This is essential for Firebase operations which don't allow undefined values
 */
export const cleanObject = (obj: any): any => {
  const cleaned: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value) && !(value instanceof Date)) {
        cleaned[key] = cleanObject(value);
      } else {
        cleaned[key] = value;
      }
    }
  }
  return cleaned;
};

/**
 * Clean an array of objects, removing undefined values from each object
 */
export const cleanObjectArray = <T>(arr: T[]): T[] => {
  return arr.map(item => cleanObject(item));
};
