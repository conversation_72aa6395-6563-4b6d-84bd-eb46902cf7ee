import React, { useState } from 'react';
import {
  Upload,
  Download,
  Edit,
  Trash2,
  Package,
  AlertTriangle,
  CheckCircle,
  X,
  FileSpreadsheet
} from 'lucide-react';
import { Product } from '../../types';
import { useProducts } from '../../hooks/useProducts';
import { parseImportFile, generateImportTemplate, ImportResult } from '../../utils/excelImport';

interface BulkOperationsProps {
  selectedProducts: Product[];
  onClose: () => void;
  onClearSelection: () => void;
}

const BulkOperations: React.FC<BulkOperationsProps> = ({
  selectedProducts,
  onClose,
  onClearSelection
}) => {
  const { updateProduct, deleteProduct, createProduct } = useProducts();
  const [operation, setOperation] = useState<'update' | 'delete' | 'export' | 'import' | null>(null);
  const [bulkUpdateData, setBulkUpdateData] = useState({
    category: '',
    priceAdjustment: '',
    adjustmentType: 'percentage' as 'percentage' | 'fixed',
    stockAdjustment: '',
    reorderLevel: '',
    isActive: ''
  });
  const [processing, setProcessing] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [showImportPreview, setShowImportPreview] = useState(false);

  // Handle bulk price update
  const handleBulkUpdate = async () => {
    if (selectedProducts.length === 0) return;

    setProcessing(true);
    try {
      for (const product of selectedProducts) {
        const updates: Partial<Product> = {};

        // Category update
        if (bulkUpdateData.category) {
          updates.category = bulkUpdateData.category;
        }

        // Price adjustment
        if (bulkUpdateData.priceAdjustment) {
          const adjustment = parseFloat(bulkUpdateData.priceAdjustment);
          if (bulkUpdateData.adjustmentType === 'percentage') {
            updates.price = product.price * (1 + adjustment / 100);
          } else {
            updates.price = product.price + adjustment;
          }
        }

        // Stock adjustment
        if (bulkUpdateData.stockAdjustment) {
          const adjustment = parseInt(bulkUpdateData.stockAdjustment);
          updates.stockQuantity = Math.max(0, product.stockQuantity + adjustment);
        }

        // Reorder level
        if (bulkUpdateData.reorderLevel) {
          updates.reorderLevel = parseInt(bulkUpdateData.reorderLevel);
        }

        // Active status
        if (bulkUpdateData.isActive !== '') {
          updates.isActive = bulkUpdateData.isActive === 'true';
        }

        if (Object.keys(updates).length > 0) {
          await updateProduct(product.id, updates);
        }
      }

      alert(`Successfully updated ${selectedProducts.length} products`);
      onClearSelection();
      onClose();
    } catch (error) {
      console.error('Bulk update error:', error);
      alert('Error updating products. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedProducts.length === 0) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedProducts.length} products? This action cannot be undone.`
    );

    if (!confirmed) return;

    setProcessing(true);
    try {
      for (const product of selectedProducts) {
        await deleteProduct(product.id);
      }

      alert(`Successfully deleted ${selectedProducts.length} products`);
      onClearSelection();
      onClose();
    } catch (error) {
      console.error('Bulk delete error:', error);
      alert('Error deleting products. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  // Export products to CSV
  const handleExport = () => {
    const csvHeaders = [
      'ID',
      'Name',
      'Description',
      'Category',
      'Price',
      'Stock Quantity',
      'Reorder Level',
      'Has Expiry',
      'Expiry Date',
      'Is Active',
      'Created At'
    ];

    const csvData = selectedProducts.map(product => [
      product.id,
      product.name,
      product.description,
      product.category,
      product.price,
      product.stockQuantity,
      product.reorderLevel,
      product.hasExpiry,
      product.expiryDate?.toISOString() || '',
      product.isActive,
      product.createdAt.toISOString()
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `products-export-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);

    alert(`Exported ${selectedProducts.length} products to CSV`);
  };

  // Handle file parsing and preview
  const handleFileSelect = async (file: File | null) => {
    setImportFile(file);
    setImportResult(null);
    setShowImportPreview(false);

    if (!file) return;

    setProcessing(true);
    try {
      const result = await parseImportFile(file);
      setImportResult(result);

      if (result.success && result.data && result.data.length > 0) {
        setShowImportPreview(true);
      } else if (result.errors) {
        alert(`Import validation failed:\n${result.errors.join('\n')}`);
      }
    } catch (error) {
      console.error('File parsing error:', error);
      alert('Error parsing file. Please check the file format.');
    } finally {
      setProcessing(false);
    }
  };

  // Handle actual import after preview confirmation
  const handleConfirmImport = async () => {
    if (!importResult || !importResult.data) return;

    // Show confirmation dialog
    const totalProducts = importResult.data.length;
    const confirmMessage = `Are you sure you want to import ${totalProducts} product${totalProducts > 1 ? 's' : ''} into your inventory?\n\nThis action cannot be undone.`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    setProcessing(true);
    try {
      let importedCount = 0;
      let failedCount = 0;
      const failedProducts: string[] = [];

      for (const productData of importResult.data) {
        try {
          await createProduct(productData as Omit<Product, 'id' | 'createdAt' | 'updatedAt'>);
          importedCount++;
        } catch (error) {
          console.error('Error creating product:', error);
          failedCount++;
          failedProducts.push(productData.name || 'Unknown product');
        }
      }

      // Show detailed results
      if (importedCount > 0) {
        let message = `✅ Successfully imported ${importedCount} product${importedCount > 1 ? 's' : ''} into your inventory!`;

        if (failedCount > 0) {
          message += `\n\n⚠️ ${failedCount} product${failedCount > 1 ? 's' : ''} failed to import:`;
          message += `\n${failedProducts.slice(0, 5).join('\n')}`;
          if (failedProducts.length > 5) {
            message += `\n... and ${failedProducts.length - 5} more`;
          }
        }

        alert(message);
      } else {
        alert('❌ No products were imported. Please check the data and try again.');
      }

      // Reset the form
      setImportFile(null);
      setImportResult(null);
      setShowImportPreview(false);
      onClearSelection();
      onClose();
    } catch (error) {
      console.error('Import error:', error);
      alert('❌ Error importing products. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  // Handle template download
  const handleDownloadTemplate = () => {
    generateImportTemplate();
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Bulk Operations ({selectedProducts.length} products selected)
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {!operation ? (
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setOperation('update')}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
            >
              <Edit className="h-6 w-6 text-blue-600 mb-2" />
              <h4 className="font-medium text-gray-900">Bulk Update</h4>
              <p className="text-sm text-gray-500">Update multiple products at once</p>
            </button>

            <button
              onClick={() => setOperation('delete')}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
            >
              <Trash2 className="h-6 w-6 text-red-600 mb-2" />
              <h4 className="font-medium text-gray-900">Bulk Delete</h4>
              <p className="text-sm text-gray-500">Delete selected products</p>
            </button>

            <button
              onClick={() => setOperation('export')}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
            >
              <Download className="h-6 w-6 text-green-600 mb-2" />
              <h4 className="font-medium text-gray-900">Export</h4>
              <p className="text-sm text-gray-500">Export to CSV file</p>
            </button>

            <button
              onClick={() => setOperation('import')}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
            >
              <Upload className="h-6 w-6 text-purple-600 mb-2" />
              <h4 className="font-medium text-gray-900">Import</h4>
              <p className="text-sm text-gray-500">Import from Excel or CSV file</p>
            </button>
          </div>
        ) : operation === 'update' ? (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Bulk Update Products</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category (leave empty to keep current)
                </label>
                <input
                  type="text"
                  value={bulkUpdateData.category}
                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="New category"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price Adjustment
                </label>
                <div className="flex">
                  <select
                    value={bulkUpdateData.adjustmentType}
                    onChange={(e) => setBulkUpdateData(prev => ({ 
                      ...prev, 
                      adjustmentType: e.target.value as 'percentage' | 'fixed' 
                    }))}
                    className="border border-gray-300 rounded-l-md px-3 py-2"
                  >
                    <option value="percentage">%</option>
                    <option value="fixed">KSh</option>
                  </select>
                  <input
                    type="number"
                    value={bulkUpdateData.priceAdjustment}
                    onChange={(e) => setBulkUpdateData(prev => ({ ...prev, priceAdjustment: e.target.value }))}
                    className="flex-1 border border-gray-300 rounded-r-md px-3 py-2"
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stock Adjustment
                </label>
                <input
                  type="number"
                  value={bulkUpdateData.stockAdjustment}
                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, stockAdjustment: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="+/- quantity"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reorder Level
                </label>
                <input
                  type="number"
                  value={bulkUpdateData.reorderLevel}
                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, reorderLevel: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="New reorder level"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Active Status
                </label>
                <select
                  value={bulkUpdateData.isActive}
                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, isActive: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Keep current</option>
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={() => setOperation(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Back
              </button>
              <button
                onClick={handleBulkUpdate}
                disabled={processing}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {processing ? 'Updating...' : 'Update Products'}
              </button>
            </div>
          </div>
        ) : operation === 'delete' ? (
          <div className="space-y-4">
            <div className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <h4 className="font-medium">Delete Selected Products</h4>
            </div>
            
            <p className="text-gray-600">
              You are about to delete {selectedProducts.length} products. This action cannot be undone.
            </p>

            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-800">
                Products to be deleted:
              </p>
              <ul className="mt-2 text-sm text-red-700 max-h-32 overflow-y-auto">
                {selectedProducts.slice(0, 10).map(product => (
                  <li key={product.id}>• {product.name}</li>
                ))}
                {selectedProducts.length > 10 && (
                  <li>• ... and {selectedProducts.length - 10} more</li>
                )}
              </ul>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={() => setOperation(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Back
              </button>
              <button
                onClick={handleBulkDelete}
                disabled={processing}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {processing ? 'Deleting...' : 'Delete Products'}
              </button>
            </div>
          </div>
        ) : operation === 'export' ? (
          <div className="space-y-4">
            <div className="flex items-center text-green-600">
              <Download className="h-5 w-5 mr-2" />
              <h4 className="font-medium">Export Products</h4>
            </div>
            
            <p className="text-gray-600">
              Export {selectedProducts.length} selected products to a CSV file.
            </p>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={() => setOperation(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Back
              </button>
              <button
                onClick={handleExport}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Export to CSV
              </button>
            </div>
          </div>
        ) : operation === 'import' ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-purple-600">
                <Upload className="h-5 w-5 mr-2" />
                <h4 className="font-medium">Import Products</h4>
              </div>
              <button
                onClick={handleDownloadTemplate}
                className="flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <FileSpreadsheet className="h-4 w-4 mr-1" />
                Download Template
              </button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <p className="text-sm text-blue-800">
                <strong>Supported formats:</strong> Excel (.xlsx, .xls) and CSV (.csv) files
              </p>
              <p className="text-sm text-blue-700 mt-1">
                <strong>Required columns:</strong> Name, Category, Price, Stock Quantity
              </p>
              <p className="text-sm text-blue-700">
                <strong>Optional columns:</strong> Description, Reorder Level, Has Expiry, Expiry Date, Is Active
              </p>
            </div>

            {!showImportPreview ? (
              <div className="space-y-4">
                <div>
                  <label htmlFor="file-input" className="block text-sm font-medium text-gray-700 mb-2">
                    Select File
                  </label>
                  <input
                    id="file-input"
                    type="file"
                    accept=".csv,.xlsx,.xls"
                    onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                  {processing && (
                    <p className="text-sm text-gray-600 mt-2">Parsing file...</p>
                  )}
                </div>

                {importFile && !processing && importResult && !importResult.success && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <div className="flex items-center text-red-800">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      <span className="font-medium">File validation failed</span>
                    </div>
                    {importResult.errors && (
                      <ul className="text-sm text-red-700 mt-1 list-disc list-inside">
                        {importResult.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-green-800">
                      <CheckCircle className="h-5 w-5 mr-2" />
                      <span className="font-medium">Import Preview Ready</span>
                    </div>
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                      {importResult?.data?.length || 0} products
                    </span>
                  </div>
                  <p className="text-sm text-green-700 mt-2">
                    Review the products below and click "Execute Import" to add them to your inventory.
                  </p>
                  {importResult?.warnings && importResult.warnings.length > 0 && (
                    <div className="mt-3 bg-yellow-50 border border-yellow-200 rounded-md p-2">
                      <p className="text-sm text-yellow-800 font-medium flex items-center">
                        <AlertTriangle className="h-4 w-4 mr-1" />
                        Warnings:
                      </p>
                      <ul className="text-sm text-yellow-700 mt-1 list-disc list-inside ml-5">
                        {importResult.warnings.map((warning, index) => (
                          <li key={index}>{warning}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                    <h5 className="text-sm font-medium text-gray-900">Products to Import (Preview)</h5>
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    <table className="min-w-full text-sm">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          <th className="px-4 py-2 text-left font-medium text-gray-700">#</th>
                          <th className="px-4 py-2 text-left font-medium text-gray-700">Name</th>
                          <th className="px-4 py-2 text-left font-medium text-gray-700">Category</th>
                          <th className="px-4 py-2 text-left font-medium text-gray-700">Price</th>
                          <th className="px-4 py-2 text-left font-medium text-gray-700">Stock</th>
                        </tr>
                      </thead>
                      <tbody>
                        {importResult?.data?.slice(0, 15).map((product, index) => (
                          <tr key={index} className="border-t border-gray-200 hover:bg-gray-50">
                            <td className="px-4 py-2 text-gray-500">{index + 1}</td>
                            <td className="px-4 py-2 font-medium">{product.name}</td>
                            <td className="px-4 py-2">{product.category}</td>
                            <td className="px-4 py-2 text-green-600 font-medium">KSh {product.price?.toLocaleString()}</td>
                            <td className="px-4 py-2">{product.stockQuantity}</td>
                          </tr>
                        ))}
                        {(importResult?.data?.length || 0) > 15 && (
                          <tr className="border-t border-gray-200 bg-gray-50">
                            <td colSpan={5} className="px-4 py-3 text-center text-gray-500 font-medium">
                              ... and {(importResult?.data?.length || 0) - 15} more products
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center pt-4">
              <button
                onClick={() => {
                  setOperation(null);
                  setImportFile(null);
                  setImportResult(null);
                  setShowImportPreview(false);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Back
              </button>

              <div className="flex space-x-3">
                {!showImportPreview && (
                  <button
                    onClick={handleDownloadTemplate}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Download Template
                  </button>
                )}

                {showImportPreview && (
                  <>
                    <button
                      onClick={() => {
                        setImportFile(null);
                        setImportResult(null);
                        setShowImportPreview(false);
                      }}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Choose Different File
                    </button>
                    <button
                      onClick={handleConfirmImport}
                      disabled={processing || !importResult?.data || importResult.data.length === 0}
                      className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      {processing ? 'Executing Import...' : `Execute Import (${importResult?.data?.length || 0} products)`}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default BulkOperations;
