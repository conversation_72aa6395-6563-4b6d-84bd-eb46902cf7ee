import { cleanObject } from './objectUtils';

/**
 * Demonstration of the Firebase undefined value issue and our fix
 */

// Example of problematic transaction data that would cause Firebase errors
const problematicTransactionData = {
  items: [
    {
      id: 'item1',
      type: 'product',
      itemId: 'prod1',
      name: 'Test Product',
      quantity: 1,
      unitPrice: 100,
      totalPrice: 100,
      notes: undefined, // ❌ This would cause Firebase error
    }
  ],
  subtotal: 100,
  discount: 0,
  total: 100,
  paymentMethod: 'cash',
  customerId: undefined, // ❌ This would cause Firebase error
  attendantId: 'user123',
  status: 'completed',
  notes: undefined, // ❌ This would cause Firebase error
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Example of cleaned transaction data that Firebase will accept
const cleanedTransactionData = cleanObject(problematicTransactionData);

console.log('=== Firebase Undefined Value Issue Demo ===\n');

console.log('❌ PROBLEMATIC DATA (would cause Firebase error):');
console.log(JSON.stringify(problematicTransactionData, null, 2));

console.log('\n✅ CLEANED DATA (Firebase-safe):');
console.log(JSON.stringify(cleanedTransactionData, null, 2));

console.log('\n🔍 DIFFERENCES:');
console.log('- Removed undefined "notes" from transaction item');
console.log('- Removed undefined "customerId" from transaction');
console.log('- Removed undefined "notes" from transaction');
console.log('- Preserved all other fields including dates and arrays');

// Verify that undefined values are actually removed
const hasUndefinedValues = (obj: any): boolean => {
  return JSON.stringify(obj).includes('undefined');
};

console.log('\n📊 VALIDATION:');
console.log(`Original data has undefined values: ${hasUndefinedValues(problematicTransactionData)}`);
console.log(`Cleaned data has undefined values: ${hasUndefinedValues(cleanedTransactionData)}`);

export { problematicTransactionData, cleanedTransactionData };
