{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { collection, doc, getDoc, addDoc, updateDoc, deleteDoc, query, orderBy, onSnapshot, serverTimestamp } from 'firebase/firestore';\nimport { db } from '../config/firebase';\nexport const useProducts = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Real-time listener for products\n  useEffect(() => {\n    const productsQuery = query(collection(db, 'products'), orderBy('category'), orderBy('name'));\n    const unsubscribe = onSnapshot(productsQuery, snapshot => {\n      const productsData = [];\n      snapshot.forEach(doc => {\n        var _data$expiryDate, _data$createdAt, _data$updatedAt;\n        const data = doc.data();\n        productsData.push({\n          id: doc.id,\n          name: data.name || '',\n          description: data.description || '',\n          price: data.price || 0,\n          category: data.category || '',\n          stockQuantity: data.stockQuantity || 0,\n          reorderLevel: data.reorderLevel || 0,\n          hasExpiry: data.hasExpiry || false,\n          expiryDate: (_data$expiryDate = data.expiryDate) === null || _data$expiryDate === void 0 ? void 0 : _data$expiryDate.toDate(),\n          isActive: data.isActive !== false,\n          createdAt: ((_data$createdAt = data.createdAt) === null || _data$createdAt === void 0 ? void 0 : _data$createdAt.toDate()) || new Date(),\n          updatedAt: ((_data$updatedAt = data.updatedAt) === null || _data$updatedAt === void 0 ? void 0 : _data$updatedAt.toDate()) || new Date()\n        });\n      });\n      setProducts(productsData);\n      setLoading(false);\n      setError(null);\n    }, error => {\n      console.error('Error fetching products:', error);\n      setError('Failed to fetch products');\n      setLoading(false);\n    });\n    return () => unsubscribe();\n  }, []);\n  const createProduct = async productData => {\n    try {\n      setError(null);\n\n      // Clean the product data to remove any undefined values\n      const cleanProductData = {\n        name: productData.name || '',\n        description: productData.description || '',\n        price: productData.price || 0,\n        category: productData.category || '',\n        stockQuantity: productData.stockQuantity || 0,\n        reorderLevel: productData.reorderLevel || 0,\n        hasExpiry: productData.hasExpiry || false,\n        isActive: productData.isActive !== false,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      };\n\n      // Only add expiryDate if it exists and hasExpiry is true\n      if (productData.hasExpiry && productData.expiryDate) {\n        cleanProductData.expiryDate = productData.expiryDate;\n      }\n      await addDoc(collection(db, 'products'), cleanProductData);\n    } catch (error) {\n      console.error('Error creating product:', error);\n      setError('Failed to create product');\n      throw error;\n    }\n  };\n  const updateProduct = async (productId, updates) => {\n    try {\n      setError(null);\n      const {\n        id,\n        createdAt,\n        ...updateData\n      } = updates;\n\n      // Clean the update data to remove any undefined values\n      const cleanUpdateData = {\n        updatedAt: serverTimestamp()\n      };\n\n      // Only add fields that are not undefined\n      Object.keys(updateData).forEach(key => {\n        const value = updateData[key];\n        if (value !== undefined) {\n          cleanUpdateData[key] = value;\n        }\n      });\n\n      // Special handling for expiryDate - only include if hasExpiry is true\n      if (updateData.hasExpiry === false) {\n        // If hasExpiry is being set to false, remove expiryDate\n        cleanUpdateData.expiryDate = null;\n      } else if (updateData.hasExpiry && updateData.expiryDate) {\n        cleanUpdateData.expiryDate = updateData.expiryDate;\n      }\n      await updateDoc(doc(db, 'products', productId), cleanUpdateData);\n    } catch (error) {\n      console.error('Error updating product:', error);\n      setError('Failed to update product');\n      throw error;\n    }\n  };\n  const deleteProduct = async productId => {\n    try {\n      setError(null);\n      await deleteDoc(doc(db, 'products', productId));\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      setError('Failed to delete product');\n      throw error;\n    }\n  };\n  const updateStock = async (productId, newQuantity) => {\n    try {\n      setError(null);\n      await updateDoc(doc(db, 'products', productId), {\n        stockQuantity: newQuantity,\n        updatedAt: serverTimestamp()\n      });\n    } catch (error) {\n      console.error('Error updating stock:', error);\n      setError('Failed to update stock');\n      throw error;\n    }\n  };\n  const getProductById = async productId => {\n    try {\n      const productDoc = await getDoc(doc(db, 'products', productId));\n      if (productDoc.exists()) {\n        var _data$expiryDate2, _data$createdAt2, _data$updatedAt2;\n        const data = productDoc.data();\n        return {\n          id: productDoc.id,\n          name: data.name || '',\n          description: data.description || '',\n          price: data.price || 0,\n          category: data.category || '',\n          stockQuantity: data.stockQuantity || 0,\n          reorderLevel: data.reorderLevel || 0,\n          hasExpiry: data.hasExpiry || false,\n          expiryDate: (_data$expiryDate2 = data.expiryDate) === null || _data$expiryDate2 === void 0 ? void 0 : _data$expiryDate2.toDate(),\n          isActive: data.isActive !== false,\n          createdAt: ((_data$createdAt2 = data.createdAt) === null || _data$createdAt2 === void 0 ? void 0 : _data$createdAt2.toDate()) || new Date(),\n          updatedAt: ((_data$updatedAt2 = data.updatedAt) === null || _data$updatedAt2 === void 0 ? void 0 : _data$updatedAt2.toDate()) || new Date()\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching product:', error);\n      throw error;\n    }\n  };\n  const getProductsByCategory = category => {\n    return products.filter(product => product.category === category && product.isActive);\n  };\n  const getActiveProducts = () => {\n    return products.filter(product => product.isActive);\n  };\n  const getInStockProducts = () => {\n    return products.filter(product => product.isActive && product.stockQuantity > 0);\n  };\n  const getLowStockProducts = () => {\n    return products.filter(product => product.isActive && product.stockQuantity <= product.reorderLevel);\n  };\n  const getExpiringProducts = (daysAhead = 30) => {\n    const futureDate = new Date();\n    futureDate.setDate(futureDate.getDate() + daysAhead);\n    return products.filter(product => product.isActive && product.hasExpiry && product.expiryDate && product.expiryDate <= futureDate);\n  };\n  const getProductCategories = () => {\n    const categories = [...new Set(products.map(product => product.category))];\n    return categories.sort();\n  };\n  const searchProducts = searchTerm => {\n    if (!searchTerm.trim()) return getActiveProducts();\n    const term = searchTerm.toLowerCase();\n    return products.filter(product => product.isActive && (product.name.toLowerCase().includes(term) || product.description.toLowerCase().includes(term) || product.category.toLowerCase().includes(term)));\n  };\n  return {\n    products,\n    loading,\n    error,\n    createProduct,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getProductById,\n    getProductsByCategory,\n    getActiveProducts,\n    getInStockProducts,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories,\n    searchProducts\n  };\n};\n_s(useProducts, \"3+N/VFIgZOBgubN9oS5aTzm2qqY=\");", "map": {"version": 3, "names": ["useState", "useEffect", "collection", "doc", "getDoc", "addDoc", "updateDoc", "deleteDoc", "query", "orderBy", "onSnapshot", "serverTimestamp", "db", "useProducts", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "productsQuery", "unsubscribe", "snapshot", "productsData", "for<PERSON>ach", "_data$expiryDate", "_data$createdAt", "_data$updatedAt", "data", "push", "id", "name", "description", "price", "category", "stockQuantity", "reorderLevel", "hasEx<PERSON>ry", "expiryDate", "toDate", "isActive", "createdAt", "Date", "updatedAt", "console", "createProduct", "productData", "cleanProductData", "updateProduct", "productId", "updates", "updateData", "cleanUpdateData", "Object", "keys", "key", "value", "undefined", "deleteProduct", "updateStock", "newQuantity", "getProductById", "productDoc", "exists", "_data$expiryDate2", "_data$createdAt2", "_data$updatedAt2", "getProductsByCategory", "filter", "product", "getActiveProducts", "getInStockProducts", "getLowStockProducts", "getExpiringProducts", "daysAhead", "futureDate", "setDate", "getDate", "getProductCategories", "categories", "Set", "map", "sort", "searchProducts", "searchTerm", "trim", "term", "toLowerCase", "includes"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useProducts.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport {\n  collection,\n  doc,\n  getDoc,\n  addDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  orderBy,\n  onSnapshot,\n  serverTimestamp\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { Product } from '../types';\n\nexport const useProducts = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Real-time listener for products\n  useEffect(() => {\n    const productsQuery = query(\n      collection(db, 'products'),\n      orderBy('category'),\n      orderBy('name')\n    );\n\n    const unsubscribe = onSnapshot(\n      productsQuery,\n      (snapshot) => {\n        const productsData: Product[] = [];\n        snapshot.forEach((doc) => {\n          const data = doc.data();\n          productsData.push({\n            id: doc.id,\n            name: data.name || '',\n            description: data.description || '',\n            price: data.price || 0,\n            category: data.category || '',\n            stockQuantity: data.stockQuantity || 0,\n            reorderLevel: data.reorderLevel || 0,\n            hasExpiry: data.hasExpiry || false,\n            expiryDate: data.expiryDate?.toDate(),\n            isActive: data.isActive !== false,\n            createdAt: data.createdAt?.toDate() || new Date(),\n            updatedAt: data.updatedAt?.toDate() || new Date(),\n          });\n        });\n        setProducts(productsData);\n        setLoading(false);\n        setError(null);\n      },\n      (error) => {\n        console.error('Error fetching products:', error);\n        setError('Failed to fetch products');\n        setLoading(false);\n      }\n    );\n\n    return () => unsubscribe();\n  }, []);\n\n  const createProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    try {\n      setError(null);\n\n      // Clean the product data to remove any undefined values\n      const cleanProductData: any = {\n        name: productData.name || '',\n        description: productData.description || '',\n        price: productData.price || 0,\n        category: productData.category || '',\n        stockQuantity: productData.stockQuantity || 0,\n        reorderLevel: productData.reorderLevel || 0,\n        hasExpiry: productData.hasExpiry || false,\n        isActive: productData.isActive !== false,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      };\n\n      // Only add expiryDate if it exists and hasExpiry is true\n      if (productData.hasExpiry && productData.expiryDate) {\n        cleanProductData.expiryDate = productData.expiryDate;\n      }\n\n      await addDoc(collection(db, 'products'), cleanProductData);\n    } catch (error) {\n      console.error('Error creating product:', error);\n      setError('Failed to create product');\n      throw error;\n    }\n  };\n\n  const updateProduct = async (productId: string, updates: Partial<Product>) => {\n    try {\n      setError(null);\n      const { id, createdAt, ...updateData } = updates;\n\n      // Clean the update data to remove any undefined values\n      const cleanUpdateData: any = {\n        updatedAt: serverTimestamp(),\n      };\n\n      // Only add fields that are not undefined\n      Object.keys(updateData).forEach(key => {\n        const value = (updateData as any)[key];\n        if (value !== undefined) {\n          cleanUpdateData[key] = value;\n        }\n      });\n\n      // Special handling for expiryDate - only include if hasExpiry is true\n      if (updateData.hasExpiry === false) {\n        // If hasExpiry is being set to false, remove expiryDate\n        cleanUpdateData.expiryDate = null;\n      } else if (updateData.hasExpiry && updateData.expiryDate) {\n        cleanUpdateData.expiryDate = updateData.expiryDate;\n      }\n\n      await updateDoc(doc(db, 'products', productId), cleanUpdateData);\n    } catch (error) {\n      console.error('Error updating product:', error);\n      setError('Failed to update product');\n      throw error;\n    }\n  };\n\n  const deleteProduct = async (productId: string) => {\n    try {\n      setError(null);\n      await deleteDoc(doc(db, 'products', productId));\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      setError('Failed to delete product');\n      throw error;\n    }\n  };\n\n  const updateStock = async (productId: string, newQuantity: number) => {\n    try {\n      setError(null);\n      await updateDoc(doc(db, 'products', productId), {\n        stockQuantity: newQuantity,\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error updating stock:', error);\n      setError('Failed to update stock');\n      throw error;\n    }\n  };\n\n  const getProductById = async (productId: string): Promise<Product | null> => {\n    try {\n      const productDoc = await getDoc(doc(db, 'products', productId));\n      if (productDoc.exists()) {\n        const data = productDoc.data();\n        return {\n          id: productDoc.id,\n          name: data.name || '',\n          description: data.description || '',\n          price: data.price || 0,\n          category: data.category || '',\n          stockQuantity: data.stockQuantity || 0,\n          reorderLevel: data.reorderLevel || 0,\n          hasExpiry: data.hasExpiry || false,\n          expiryDate: data.expiryDate?.toDate(),\n          isActive: data.isActive !== false,\n          createdAt: data.createdAt?.toDate() || new Date(),\n          updatedAt: data.updatedAt?.toDate() || new Date(),\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching product:', error);\n      throw error;\n    }\n  };\n\n  const getProductsByCategory = (category: string) => {\n    return products.filter(product => product.category === category && product.isActive);\n  };\n\n  const getActiveProducts = () => {\n    return products.filter(product => product.isActive);\n  };\n\n  const getInStockProducts = () => {\n    return products.filter(product => product.isActive && product.stockQuantity > 0);\n  };\n\n  const getLowStockProducts = () => {\n    return products.filter(product => \n      product.isActive && product.stockQuantity <= product.reorderLevel\n    );\n  };\n\n  const getExpiringProducts = (daysAhead: number = 30) => {\n    const futureDate = new Date();\n    futureDate.setDate(futureDate.getDate() + daysAhead);\n    \n    return products.filter(product => \n      product.isActive && \n      product.hasExpiry && \n      product.expiryDate && \n      product.expiryDate <= futureDate\n    );\n  };\n\n  const getProductCategories = () => {\n    const categories = [...new Set(products.map(product => product.category))];\n    return categories.sort();\n  };\n\n  const searchProducts = (searchTerm: string) => {\n    if (!searchTerm.trim()) return getActiveProducts();\n    \n    const term = searchTerm.toLowerCase();\n    return products.filter(product => \n      product.isActive && (\n        product.name.toLowerCase().includes(term) ||\n        product.description.toLowerCase().includes(term) ||\n        product.category.toLowerCase().includes(term)\n      )\n    );\n  };\n\n  return {\n    products,\n    loading,\n    error,\n    createProduct,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getProductById,\n    getProductsByCategory,\n    getActiveProducts,\n    getInStockProducts,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories,\n    searchProducts,\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,eAAe,QACV,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;AAGvC,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoB,aAAa,GAAGb,KAAK,CACzBN,UAAU,CAACU,EAAE,EAAE,UAAU,CAAC,EAC1BH,OAAO,CAAC,UAAU,CAAC,EACnBA,OAAO,CAAC,MAAM,CAChB,CAAC;IAED,MAAMa,WAAW,GAAGZ,UAAU,CAC5BW,aAAa,EACZE,QAAQ,IAAK;MACZ,MAAMC,YAAuB,GAAG,EAAE;MAClCD,QAAQ,CAACE,OAAO,CAAEtB,GAAG,IAAK;QAAA,IAAAuB,gBAAA,EAAAC,eAAA,EAAAC,eAAA;QACxB,MAAMC,IAAI,GAAG1B,GAAG,CAAC0B,IAAI,CAAC,CAAC;QACvBL,YAAY,CAACM,IAAI,CAAC;UAChBC,EAAE,EAAE5B,GAAG,CAAC4B,EAAE;UACVC,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAE;UACrBC,WAAW,EAAEJ,IAAI,CAACI,WAAW,IAAI,EAAE;UACnCC,KAAK,EAAEL,IAAI,CAACK,KAAK,IAAI,CAAC;UACtBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,EAAE;UAC7BC,aAAa,EAAEP,IAAI,CAACO,aAAa,IAAI,CAAC;UACtCC,YAAY,EAAER,IAAI,CAACQ,YAAY,IAAI,CAAC;UACpCC,SAAS,EAAET,IAAI,CAACS,SAAS,IAAI,KAAK;UAClCC,UAAU,GAAAb,gBAAA,GAAEG,IAAI,CAACU,UAAU,cAAAb,gBAAA,uBAAfA,gBAAA,CAAiBc,MAAM,CAAC,CAAC;UACrCC,QAAQ,EAAEZ,IAAI,CAACY,QAAQ,KAAK,KAAK;UACjCC,SAAS,EAAE,EAAAf,eAAA,GAAAE,IAAI,CAACa,SAAS,cAAAf,eAAA,uBAAdA,eAAA,CAAgBa,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC,CAAC;UACjDC,SAAS,EAAE,EAAAhB,eAAA,GAAAC,IAAI,CAACe,SAAS,cAAAhB,eAAA,uBAAdA,eAAA,CAAgBY,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;MACF3B,WAAW,CAACQ,YAAY,CAAC;MACzBN,UAAU,CAAC,KAAK,CAAC;MACjBE,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,EACAD,KAAK,IAAK;MACT0B,OAAO,CAAC1B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0BAA0B,CAAC;MACpCF,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAMI,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwB,aAAa,GAAG,MAAOC,WAA4D,IAAK;IAC5F,IAAI;MACF3B,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM4B,gBAAqB,GAAG;QAC5BhB,IAAI,EAAEe,WAAW,CAACf,IAAI,IAAI,EAAE;QAC5BC,WAAW,EAAEc,WAAW,CAACd,WAAW,IAAI,EAAE;QAC1CC,KAAK,EAAEa,WAAW,CAACb,KAAK,IAAI,CAAC;QAC7BC,QAAQ,EAAEY,WAAW,CAACZ,QAAQ,IAAI,EAAE;QACpCC,aAAa,EAAEW,WAAW,CAACX,aAAa,IAAI,CAAC;QAC7CC,YAAY,EAAEU,WAAW,CAACV,YAAY,IAAI,CAAC;QAC3CC,SAAS,EAAES,WAAW,CAACT,SAAS,IAAI,KAAK;QACzCG,QAAQ,EAAEM,WAAW,CAACN,QAAQ,KAAK,KAAK;QACxCC,SAAS,EAAE/B,eAAe,CAAC,CAAC;QAC5BiC,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC;;MAED;MACA,IAAIoC,WAAW,CAACT,SAAS,IAAIS,WAAW,CAACR,UAAU,EAAE;QACnDS,gBAAgB,CAACT,UAAU,GAAGQ,WAAW,CAACR,UAAU;MACtD;MAEA,MAAMlC,MAAM,CAACH,UAAU,CAACU,EAAE,EAAE,UAAU,CAAC,EAAEoC,gBAAgB,CAAC;IAC5D,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAM8B,aAAa,GAAG,MAAAA,CAAOC,SAAiB,EAAEC,OAAyB,KAAK;IAC5E,IAAI;MACF/B,QAAQ,CAAC,IAAI,CAAC;MACd,MAAM;QAAEW,EAAE;QAAEW,SAAS;QAAE,GAAGU;MAAW,CAAC,GAAGD,OAAO;;MAEhD;MACA,MAAME,eAAoB,GAAG;QAC3BT,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC;;MAED;MACA2C,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC3B,OAAO,CAAC+B,GAAG,IAAI;QACrC,MAAMC,KAAK,GAAIL,UAAU,CAASI,GAAG,CAAC;QACtC,IAAIC,KAAK,KAAKC,SAAS,EAAE;UACvBL,eAAe,CAACG,GAAG,CAAC,GAAGC,KAAK;QAC9B;MACF,CAAC,CAAC;;MAEF;MACA,IAAIL,UAAU,CAACd,SAAS,KAAK,KAAK,EAAE;QAClC;QACAe,eAAe,CAACd,UAAU,GAAG,IAAI;MACnC,CAAC,MAAM,IAAIa,UAAU,CAACd,SAAS,IAAIc,UAAU,CAACb,UAAU,EAAE;QACxDc,eAAe,CAACd,UAAU,GAAGa,UAAU,CAACb,UAAU;MACpD;MAEA,MAAMjC,SAAS,CAACH,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEsC,SAAS,CAAC,EAAEG,eAAe,CAAC;IAClE,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMwC,aAAa,GAAG,MAAOT,SAAiB,IAAK;IACjD,IAAI;MACF9B,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMb,SAAS,CAACJ,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEsC,SAAS,CAAC,CAAC;IACjD,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMyC,WAAW,GAAG,MAAAA,CAAOV,SAAiB,EAAEW,WAAmB,KAAK;IACpE,IAAI;MACFzC,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMd,SAAS,CAACH,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEsC,SAAS,CAAC,EAAE;QAC9Cd,aAAa,EAAEyB,WAAW;QAC1BjB,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,wBAAwB,CAAC;MAClC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAM2C,cAAc,GAAG,MAAOZ,SAAiB,IAA8B;IAC3E,IAAI;MACF,MAAMa,UAAU,GAAG,MAAM3D,MAAM,CAACD,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEsC,SAAS,CAAC,CAAC;MAC/D,IAAIa,UAAU,CAACC,MAAM,CAAC,CAAC,EAAE;QAAA,IAAAC,iBAAA,EAAAC,gBAAA,EAAAC,gBAAA;QACvB,MAAMtC,IAAI,GAAGkC,UAAU,CAAClC,IAAI,CAAC,CAAC;QAC9B,OAAO;UACLE,EAAE,EAAEgC,UAAU,CAAChC,EAAE;UACjBC,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAE;UACrBC,WAAW,EAAEJ,IAAI,CAACI,WAAW,IAAI,EAAE;UACnCC,KAAK,EAAEL,IAAI,CAACK,KAAK,IAAI,CAAC;UACtBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,EAAE;UAC7BC,aAAa,EAAEP,IAAI,CAACO,aAAa,IAAI,CAAC;UACtCC,YAAY,EAAER,IAAI,CAACQ,YAAY,IAAI,CAAC;UACpCC,SAAS,EAAET,IAAI,CAACS,SAAS,IAAI,KAAK;UAClCC,UAAU,GAAA0B,iBAAA,GAAEpC,IAAI,CAACU,UAAU,cAAA0B,iBAAA,uBAAfA,iBAAA,CAAiBzB,MAAM,CAAC,CAAC;UACrCC,QAAQ,EAAEZ,IAAI,CAACY,QAAQ,KAAK,KAAK;UACjCC,SAAS,EAAE,EAAAwB,gBAAA,GAAArC,IAAI,CAACa,SAAS,cAAAwB,gBAAA,uBAAdA,gBAAA,CAAgB1B,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC,CAAC;UACjDC,SAAS,EAAE,EAAAuB,gBAAA,GAAAtC,IAAI,CAACe,SAAS,cAAAuB,gBAAA,uBAAdA,gBAAA,CAAgB3B,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC;QAClD,CAAC;MACH;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMiD,qBAAqB,GAAIjC,QAAgB,IAAK;IAClD,OAAOpB,QAAQ,CAACsD,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACnC,QAAQ,KAAKA,QAAQ,IAAImC,OAAO,CAAC7B,QAAQ,CAAC;EACtF,CAAC;EAED,MAAM8B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOxD,QAAQ,CAACsD,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAC7B,QAAQ,CAAC;EACrD,CAAC;EAED,MAAM+B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAOzD,QAAQ,CAACsD,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAC7B,QAAQ,IAAI6B,OAAO,CAAClC,aAAa,GAAG,CAAC,CAAC;EAClF,CAAC;EAED,MAAMqC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAO1D,QAAQ,CAACsD,MAAM,CAACC,OAAO,IAC5BA,OAAO,CAAC7B,QAAQ,IAAI6B,OAAO,CAAClC,aAAa,IAAIkC,OAAO,CAACjC,YACvD,CAAC;EACH,CAAC;EAED,MAAMqC,mBAAmB,GAAGA,CAACC,SAAiB,GAAG,EAAE,KAAK;IACtD,MAAMC,UAAU,GAAG,IAAIjC,IAAI,CAAC,CAAC;IAC7BiC,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,GAAGH,SAAS,CAAC;IAEpD,OAAO5D,QAAQ,CAACsD,MAAM,CAACC,OAAO,IAC5BA,OAAO,CAAC7B,QAAQ,IAChB6B,OAAO,CAAChC,SAAS,IACjBgC,OAAO,CAAC/B,UAAU,IAClB+B,OAAO,CAAC/B,UAAU,IAAIqC,UACxB,CAAC;EACH,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,UAAU,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAClE,QAAQ,CAACmE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACnC,QAAQ,CAAC,CAAC,CAAC;IAC1E,OAAO6C,UAAU,CAACG,IAAI,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,cAAc,GAAIC,UAAkB,IAAK;IAC7C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE,OAAOf,iBAAiB,CAAC,CAAC;IAElD,MAAMgB,IAAI,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;IACrC,OAAOzE,QAAQ,CAACsD,MAAM,CAACC,OAAO,IAC5BA,OAAO,CAAC7B,QAAQ,KACd6B,OAAO,CAACtC,IAAI,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,IACzCjB,OAAO,CAACrC,WAAW,CAACuD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,IAChDjB,OAAO,CAACnC,QAAQ,CAACqD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,CAEjD,CAAC;EACH,CAAC;EAED,OAAO;IACLxE,QAAQ;IACRE,OAAO;IACPE,KAAK;IACL2B,aAAa;IACbG,aAAa;IACbU,aAAa;IACbC,WAAW;IACXE,cAAc;IACdM,qBAAqB;IACrBG,iBAAiB;IACjBC,kBAAkB;IAClBC,mBAAmB;IACnBC,mBAAmB;IACnBK,oBAAoB;IACpBK;EACF,CAAC;AACH,CAAC;AAACtE,EAAA,CAtOWD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}