import React, { useState } from 'react';
import {
  Package,
  Plus,
  Search,
  AlertTriangle,
  Calendar,
  Edit,
  Trash2,
  RefreshCw,
  Upload
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useProducts } from '../../hooks/useProducts';
import { Product } from '../../types';
import ProductModal from './ProductModal';
import InventoryStats from './InventoryStats';
import StockAdjustmentModal from './StockAdjustmentModal';
import LowStockAlert from './LowStockAlert';
import BulkOperations from './BulkOperations';

const Inventory: React.FC = () => {
  const { hasPermission } = useAuth();
  const {
    products,
    loading,
    error,
    updateProduct,
    deleteProduct,
    updateStock,
    getLowStockProducts,
    getExpiringProducts,
    getProductCategories
  } = useProducts();

  const [activeView, setActiveView] = useState<'products' | 'stats' | 'alerts'>('products');
  const [showProductModal, setShowProductModal] = useState(false);
  const [showStockModal, setShowStockModal] = useState(false);
  const [showBulkOperations, setShowBulkOperations] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [adjustingStock, setAdjustingStock] = useState<Product | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [stockFilter, setStockFilter] = useState<'all' | 'low' | 'out' | 'expiring'>('all');

  // Filter products based on search, category, and stock status
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || product.category === selectedCategory;

    let matchesStock = true;
    switch (stockFilter) {
      case 'low':
        matchesStock = product.stockQuantity <= product.reorderLevel && product.stockQuantity > 0;
        break;
      case 'out':
        matchesStock = product.stockQuantity === 0;
        break;
      case 'expiring':
        const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
        matchesStock = Boolean(product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow);
        break;
    }

    return matchesSearch && matchesCategory && matchesStock;
  });

  const categories = getProductCategories();
  const lowStockProducts = getLowStockProducts();
  const expiringProducts = getExpiringProducts();

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowProductModal(true);
  };

  const handleDeleteProduct = async (productId: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteProduct(productId);
      } catch (error) {
        console.error('Error deleting product:', error);
      }
    }
  };

  const handleStockAdjustment = (product: Product) => {
    setAdjustingStock(product);
    setShowStockModal(true);
  };

  const handleToggleActive = async (product: Product) => {
    try {
      await updateProduct(product.id, { isActive: !product.isActive });
    } catch (error) {
      console.error('Error toggling product status:', error);
    }
  };



  const handleClearSelection = () => {
    setSelectedProducts([]);
  };

  const handleOpenBulkOperations = () => {
    setShowBulkOperations(true);
  };

  if (!hasPermission(['admin', 'attendant'])) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have permission to access inventory management.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Package className="h-6 w-6 text-primary-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
          </div>

          <div className="flex items-center space-x-3">
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveView('products')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeView === 'products'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Products
              </button>
              <button
                onClick={() => setActiveView('stats')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeView === 'stats'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Statistics
              </button>
              <button
                onClick={() => setActiveView('alerts')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeView === 'alerts'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Alerts
                {(lowStockProducts.length > 0 || expiringProducts.length > 0) && (
                  <span className="ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5">
                    {lowStockProducts.length + expiringProducts.length}
                  </span>
                )}
              </button>
            </div>

            {hasPermission('admin') && (
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleOpenBulkOperations}
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Bulk Import
                </button>
                <button
                  onClick={() => {
                    setEditingProduct(null);
                    setShowProductModal(true);
                  }}
                  className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Product
                </button>
              </div>
            )}
          </div>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Search and Filters - Only show for products view */}
        {activeView === 'products' && (
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div className="flex gap-3">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              <select
                value={stockFilter}
                onChange={(e) => setStockFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Stock</option>
                <option value="low">Low Stock</option>
                <option value="out">Out of Stock</option>
                <option value="expiring">Expiring Soon</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Content based on active view */}
      {activeView === 'stats' ? (
        <InventoryStats products={products} />
      ) : activeView === 'alerts' ? (
        <LowStockAlert
          lowStockProducts={lowStockProducts}
          expiringProducts={expiringProducts}
          onStockAdjust={handleStockAdjustment}
        />
      ) : (
        <>
          {/* Products Grid */}
          <div className="bg-white shadow rounded-lg p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Loading products...</p>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-12">
                <Package className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || selectedCategory || stockFilter !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Get started by adding your first product.'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    onEdit={handleEditProduct}
                    onDelete={handleDeleteProduct}
                    onStockAdjust={handleStockAdjustment}
                    onToggleActive={handleToggleActive}
                    canEdit={hasPermission('admin')}
                  />
                ))}
              </div>
            )}
          </div>
        </>
      )}

      {/* Modals */}
      {showProductModal && (
        <ProductModal
          product={editingProduct}
          onClose={() => {
            setShowProductModal(false);
            setEditingProduct(null);
          }}
        />
      )}

      {showStockModal && adjustingStock && (
        <StockAdjustmentModal
          product={adjustingStock}
          onClose={() => {
            setShowStockModal(false);
            setAdjustingStock(null);
          }}
          onAdjust={updateStock}
        />
      )}

      {showBulkOperations && (
        <BulkOperations
          selectedProducts={selectedProducts}
          onClose={() => {
            setShowBulkOperations(false);
            setSelectedProducts([]);
          }}
          onClearSelection={handleClearSelection}
        />
      )}
    </div>
  );
};

// Product Card Component
interface ProductCardProps {
  product: Product;
  onEdit: (product: Product) => void;
  onDelete: (productId: string) => void;
  onStockAdjust: (product: Product) => void;
  onToggleActive: (product: Product) => void;
  canEdit: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onEdit,
  onDelete,
  onStockAdjust,
  onToggleActive,
  canEdit
}) => {
  const isLowStock = product.stockQuantity <= product.reorderLevel;
  const isOutOfStock = product.stockQuantity === 0;
  const isExpiringSoon = product.hasExpiry && product.expiryDate &&
    product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

  const getStockStatusColor = () => {
    if (isOutOfStock) return 'text-red-600 bg-red-100';
    if (isLowStock) return 'text-orange-600 bg-orange-100';
    return 'text-green-600 bg-green-100';
  };

  const getStockStatusText = () => {
    if (isOutOfStock) return 'Out of Stock';
    if (isLowStock) return 'Low Stock';
    return 'In Stock';
  };

  return (
    <div className={`bg-white border rounded-lg p-4 ${product.isActive ? 'border-gray-200' : 'border-gray-300 bg-gray-50'}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className={`font-semibold ${product.isActive ? 'text-gray-900' : 'text-gray-500'}`}>
            {product.name}
          </h3>
          <p className={`text-sm ${product.isActive ? 'text-gray-600' : 'text-gray-400'}`}>
            {product.description}
          </p>
        </div>
        {canEdit && (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onEdit(product)}
              className="text-blue-600 hover:text-blue-800"
            >
              <Edit className="h-4 w-4" />
            </button>
            <button
              onClick={() => onDelete(product.id)}
              className="text-red-600 hover:text-red-800"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      <div className="space-y-3">
        {/* Category and Price */}
        <div className="flex items-center justify-between">
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
            {product.category}
          </span>
          <span className="font-semibold text-green-600">
            KSh {product.price.toLocaleString()}
          </span>
        </div>

        {/* Stock Information */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Stock Level:</span>
            <div className="flex items-center space-x-2">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor()}`}>
                {getStockStatusText()}
              </span>
              <button
                onClick={() => onStockAdjust(product)}
                className="text-blue-600 hover:text-blue-800"
                title="Adjust Stock"
              >
                <RefreshCw className="h-3 w-3" />
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Current:</span>
            <span className={`font-medium ${isOutOfStock ? 'text-red-600' : isLowStock ? 'text-orange-600' : 'text-green-600'}`}>
              {product.stockQuantity} units
            </span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Reorder Level:</span>
            <span className="font-medium text-gray-900">{product.reorderLevel} units</span>
          </div>
        </div>

        {/* Expiry Information */}
        {product.hasExpiry && product.expiryDate && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Expires:</span>
            <div className="flex items-center space-x-1">
              <span className={`font-medium ${isExpiringSoon ? 'text-orange-600' : 'text-gray-900'}`}>
                {product.expiryDate.toLocaleDateString()}
              </span>
              {isExpiringSoon && <Calendar className="h-3 w-3 text-orange-600" />}
            </div>
          </div>
        )}

        {/* Status Toggle */}
        {canEdit && (
          <div className="flex items-center justify-between pt-2 border-t">
            <span className="text-sm text-gray-600">Status:</span>
            <button
              onClick={() => onToggleActive(product)}
              className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                product.isActive
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {product.isActive ? 'Active' : 'Inactive'}
            </button>
          </div>
        )}

        {/* Alerts */}
        {(isLowStock || isExpiringSoon) && (
          <div className="pt-2 border-t">
            {isLowStock && (
              <div className="flex items-center text-xs text-orange-600 mb-1">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Stock below reorder level
              </div>
            )}
            {isExpiringSoon && (
              <div className="flex items-center text-xs text-orange-600">
                <Calendar className="h-3 w-3 mr-1" />
                Expires within 30 days
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Inventory;
