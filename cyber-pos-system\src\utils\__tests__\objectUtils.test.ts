import { cleanObject, cleanObjectArray } from '../objectUtils';

describe('objectUtils', () => {
  describe('cleanObject', () => {
    test('should remove undefined values from flat object', () => {
      const input = {
        name: '<PERSON>',
        age: 30,
        email: undefined,
        phone: null,
        active: true
      };

      const result = cleanObject(input);

      expect(result).toEqual({
        name: '<PERSON>',
        age: 30,
        phone: null,
        active: true
      });
      expect(result).not.toHaveProperty('email');
    });

    test('should remove undefined values from nested objects', () => {
      const input = {
        user: {
          name: '<PERSON>',
          details: {
            email: undefined,
            phone: '************',
            address: undefined
          }
        },
        settings: {
          theme: 'dark',
          notifications: undefined
        }
      };

      const result = cleanObject(input);

      expect(result).toEqual({
        user: {
          name: 'John',
          details: {
            phone: '************'
          }
        },
        settings: {
          theme: 'dark'
        }
      });
    });

    test('should preserve arrays and dates', () => {
      const date = new Date();
      const input = {
        items: [1, 2, 3],
        createdAt: date,
        tags: ['tag1', 'tag2'],
        metadata: undefined
      };

      const result = cleanObject(input);

      expect(result).toEqual({
        items: [1, 2, 3],
        createdAt: date,
        tags: ['tag1', 'tag2']
      });
      expect(result.createdAt).toBe(date);
    });

    test('should handle empty objects', () => {
      const input = {};
      const result = cleanObject(input);
      expect(result).toEqual({});
    });

    test('should handle objects with all undefined values', () => {
      const input = {
        a: undefined,
        b: undefined,
        c: undefined
      };

      const result = cleanObject(input);
      expect(result).toEqual({});
    });

    test('should handle transaction-like objects', () => {
      const input = {
        items: [
          {
            id: 'item1',
            name: 'Product 1',
            notes: undefined
          }
        ],
        subtotal: 100,
        discount: 0,
        total: 100,
        customerId: undefined,
        notes: undefined,
        createdAt: new Date()
      };

      const result = cleanObject(input);

      expect(result).toEqual({
        items: [
          {
            id: 'item1',
            name: 'Product 1'
          }
        ],
        subtotal: 100,
        discount: 0,
        total: 100,
        createdAt: input.createdAt
      });
      expect(result).not.toHaveProperty('customerId');
      expect(result).not.toHaveProperty('notes');
    });
  });

  describe('cleanObjectArray', () => {
    test('should clean all objects in array', () => {
      const input = [
        {
          id: 1,
          name: 'Item 1',
          description: undefined
        },
        {
          id: 2,
          name: 'Item 2',
          description: 'Valid description',
          notes: undefined
        }
      ];

      const result = cleanObjectArray(input);

      expect(result).toEqual([
        {
          id: 1,
          name: 'Item 1'
        },
        {
          id: 2,
          name: 'Item 2',
          description: 'Valid description'
        }
      ]);
    });

    test('should handle empty array', () => {
      const result = cleanObjectArray([]);
      expect(result).toEqual([]);
    });
  });
});
